# Task: Project Setup

## Objective

Initialize the Next.js project with TypeScript, configure essential tools, and set up the basic project structure.

## Requirements

1. Initialize a new Next.js 15+ project with TypeScript
2. Configure Tailwind CSS for styling
3. Set up ESLint and Prettier for code quality
4. Configure <PERSON><PERSON> as the package manager
5. Create the basic project structure as outlined in the technical requirements

## Implementation Steps

1. Create a new Next.js project:

   ```bash
   bun create next-app@latest b2b_portal --typescript --tailwind --eslint
   ```
2. Configure Tailwind CSS with appropriate extensions for the project:

   - Update `tailwind.config.js` to include proper content paths
   - Set up any required Tailwind plugins
3. Set up ESLint and Prettier:

   - Configure ESLint rules in `.eslintrc.js`
   - Create `.prettierrc` with project coding standards
4. Create the basic project structure:

   ```
   /src
   ├── app/                  # Next.js App Router pages
   │   ├── (portal)/         # Portal-specific routes
   │   ├── layout.tsx        # Root layout
   │   └── page.tsx          # Root page
   ├── components/           # React components
   │   ├── ui/               # Base UI components
   ├── contexts/             # React contexts
   ├── lib/                  # Core utilities
   │   ├── api/              # API integration
   │   ├── redux/            # Redux store
   │   └── i18n/             # Internationalization
   ├── types/                # TypeScript types
   └── utils/                # Utility functions
   ```
5. Set up the public directory structure:

   ```
   /public
   ├── locales/              # Translation files
   │   ├── en/               # English translations
   │   └── ar/               # Arabic translations
   └── images/               # Static images
   ```

## Deliverables

1. Initialized Next.js project with TypeScript
2. Configured Tailwind CSS
3. ESLint and Prettier setup
4. Basic project structure created
5. Public directory structure set up

## Dependencies

- Next.js 15+
- TypeScript
- Tailwind CSS
- Bun
