# Task: Layout Components

## Objective
Implement the main layout components that structure the application, including the portal layout, navigation, and global UI elements.

## Requirements

1. Create the main portal layout structure
2. Implement responsive navigation
3. Create header components with search, language toggle, and theme toggle
4. Implement sidebar navigation for the portal
5. Ensure layouts work with both LTR and RTL directions

## Implementation Steps

### 1. Main Layout Components

- Create Providers wrapper (`src/components/Providers.tsx`):
  ```typescript
  // Wrap application with Redux Provider
  // Include Theme and Language context providers
  ```

- Create Portal Layout Content (`src/components/PortalLayoutContent.tsx`):
  ```typescript
  // Main layout structure with header, sidebar, and content area
  // Responsive design with mobile considerations
  ```

### 2. Header Components

- Create Language Toggle (`src/components/LanguageToggle.tsx`):
  ```typescript
  // Toggle between English and Arabic
  // Show current language indicator
  // Use Language Context
  ```

- Create Theme Toggle (`src/components/ThemeToggle.tsx`):
  ```typescript
  // Toggle between light and dark themes
  // Show current theme indicator
  // Use Theme Context
  ```

- Create Search Autocomplete (`src/components/SearchAutocomplete.tsx`):
  ```typescript
  // Search input with autocomplete
  // Results dropdown
  // Recent searches
  ```

- Create Header Order component (`src/components/HeaderOrder.tsx`):
  ```typescript
  // Display current active order
  // Order selection dropdown
  // Quick order actions
  ```

- Create Wishlist Notification (`src/components/WishlistNotification.tsx`):
  ```typescript
  // Wishlist icon with count
  // Notification for wishlist updates
  ```

### 3. Navigation Components

- Create Sidebar Navigation (`src/components/SidebarNavigation.tsx`):
  ```typescript
  // Main navigation links
  // Active state highlighting
  // Collapsible sections
  // Mobile responsive behavior
  ```

- Create Breadcrumbs (`src/components/Breadcrumbs.tsx`):
  ```typescript
  // Show current location in site hierarchy
  // Links to parent pages
  ```

### 4. Page Layout Templates

- Create Dashboard Layout (`src/app/(portal)/dashboard/layout.tsx`):
  ```typescript
  // Dashboard-specific layout
  // Widget grid system
  ```

- Create Product Catalog Layout (`src/app/(portal)/products/layout.tsx`):
  ```typescript
  // Product listing layout with filters sidebar
  // Grid/list view toggle
  ```

- Create Order Management Layout (`src/app/(portal)/orders/layout.tsx`):
  ```typescript
  // Order listing and detail views
  // Order status visualization
  ```

### 5. RTL Support for Layouts

- Update layout components to support RTL:
  ```typescript
  // Use dir attribute from Language Context
  // Apply RTL-specific styles and positioning
  ```

## Deliverables

1. Complete portal layout structure
2. Header components (Language Toggle, Theme Toggle, Search, Order selector)
3. Navigation components (Sidebar, Breadcrumbs)
4. Page-specific layout templates
5. RTL support for all layout components

## Dependencies

- Task 001: Project Setup
- Task 002: Core Architecture
- Task 003: Base UI Components

## Estimated Time
6 hours