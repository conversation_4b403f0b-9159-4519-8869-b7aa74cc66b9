# Task: Mock API Integration

## Objective
Implement mock API services that simulate backend functionality for product catalog, order management, user authentication, and other core features.

## Requirements

1. Create mock API endpoints for product catalog and search
2. Implement mock API for order management
3. Set up mock authentication services
4. Create mock contract and pricing API
5. Implement data persistence using local storage or IndexedDB

## Implementation Steps

### 1. API Client Setup

- Create API client configuration (`src/lib/api/client.ts`):
  ```typescript
  // Base API configuration
  // Request/response interceptors
  // Error handling
  ```

- Create API response types (`src/types/api.ts`):
  ```typescript
  // API response interfaces
  // Error response types
  // Pagination types
  ```

### 2. Product Catalog API

- Create product API service (`src/lib/api/products.ts`):
  ```typescript
  // fetchProducts(filters): Get product list with filtering
  // fetchProductById(id): Get single product details
  // searchProducts(query): Search products
  // fetchCategories(): Get product categories
  ```

- Create mock product data (`src/lib/api/mock/products.ts`):
  ```typescript
  // Mock product data
  // Product categories
  // Product attributes
  ```

### 3. Order Management API

- Create order API service (`src/lib/api/orders.ts`):
  ```typescript
  // fetchOrders(): Get list of orders
  // fetchOrderById(id): Get single order details
  // createOrder(data): Create new order
  // updateOrder(id, data): Update existing order
  // changeOrderState(id, state): Change order state
  ```

- Create mock order data (`src/lib/api/mock/orders.ts`):
  ```typescript
  // Mock order data
  // Order state transitions
  ```

### 4. Authentication API

- Create authentication API service (`src/lib/api/auth.ts`):
  ```typescript
  // login(credentials): Authenticate user
  // logout(): End user session
  // getCurrentUser(): Get current user info
  // refreshToken(): Refresh authentication token
  ```

- Create mock user data (`src/lib/api/mock/users.ts`):
  ```typescript
  // Mock user data
  // User roles and permissions
  ```

### 5. Contract and Pricing API

- Create contract API service (`src/lib/api/contracts.ts`):
  ```typescript
  // fetchContracts(): Get list of contracts
  // fetchContractById(id): Get single contract details
  // fetchPriceLists(): Get price lists
  ```

- Create mock contract data (`src/lib/api/mock/contracts.ts`):
  ```typescript
  // Mock contract data
  // Price lists
  // Contract terms
  ```

### 6. Data Persistence

- Create storage service (`src/lib/api/storage.ts`):
  ```typescript
  // Use localStorage or IndexedDB
  // Store and retrieve data
  // Clear data
  ```

- Implement data persistence in API services:
  ```typescript
  // Save changes to storage
  // Load initial data from storage
  ```

### 7. API Integration with Redux

- Update Redux slices to use API services:
  ```typescript
  // Create async thunks for API calls
  // Handle loading states
  // Handle error states
  ```

## Deliverables

1. Complete mock API services for products, orders, authentication, and contracts
2. Mock data for testing and development
3. Data persistence implementation
4. Redux integration with API services
5. API documentation (inline comments or separate doc)

## Dependencies

- Task 001: Project Setup
- Task 002: Core Architecture

## Estimated Time
8 hours