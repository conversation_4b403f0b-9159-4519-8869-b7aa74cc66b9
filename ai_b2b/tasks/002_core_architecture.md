# Task: Core Architecture Setup

## Objective
Implement the core architecture of the application, including Redux store, context providers, and type definitions.

## Requirements

1. Set up Redux store with required slices
2. Create context providers for theme and language
3. Define TypeScript interfaces for data models
4. Configure Next.js app router with proper layouts

## Implementation Steps

### 1. Redux Store Setup

- Install Redux dependencies:
  ```bash
  bun add @reduxjs/toolkit react-redux
  ```

- Create Redux store configuration in `src/lib/redux/store.ts`:
  ```typescript
  // Configure store with proper middleware and dev tools
  // Ensure SSR compatibility
  ```

- Implement the following Redux slices:
  - Orders slice (`src/lib/redux/slices/ordersSlice.ts`):
    ```typescript
    // Manage multiple orders/quotes
    // Track active order
    // Handle order state transitions
    ```
  
  - Products slice (`src/lib/redux/slices/productsSlice.ts`):
    ```typescript
    // Product catalog
    // Filtering and search
    // Product details
    ```
  
  - User slice (`src/lib/redux/slices/userSlice.ts`):
    ```typescript
    // User preferences
    // Authentication state
    ```

### 2. Context Providers

- Create Theme Context (`src/contexts/ThemeContext.tsx`):
  ```typescript
  // Light/dark theme management
  // Theme persistence
  ```

- Create Language Context (`src/contexts/LanguageContext.tsx`):
  ```typescript
  // Language switching (English/Arabic)
  // RTL support for Arabic
  // Translation function
  ```

- Create Providers wrapper component (`src/components/Providers.tsx`):
  ```typescript
  // Combine Redux Provider with Context Providers
  ```

### 3. TypeScript Interfaces

- Define data models in `src/types/index.ts`:
  ```typescript
  // Product interfaces
  // Order interfaces
  // User interfaces
  ```

### 4. Next.js App Router Configuration

- Set up root layout in `src/app/layout.tsx`:
  ```typescript
  // Include Providers wrapper
  // Set up metadata
  // Include global styles
  ```

- Create portal layout in `src/app/(portal)/layout.tsx`:
  ```typescript
  // Portal-specific layout
  ```

## Deliverables

1. Configured Redux store with slices for orders, products, and user
2. Context providers for theme and language
3. TypeScript interfaces for data models
4. Next.js app router configuration with layouts

## Dependencies

- Task 001: Project Setup
- Redux Toolkit
- React Redux

## Estimated Time
6 hours