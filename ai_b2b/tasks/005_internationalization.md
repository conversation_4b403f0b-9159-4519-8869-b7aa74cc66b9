# Task: Internationalization System

## Objective
Implement a robust internationalization (i18n) system that supports both English and Arabic languages, with proper RTL support for Arabic.

## Requirements

1. Set up language switching between English and Arabic
2. Implement RTL layout support for Arabic
3. Create translation files for all UI elements
4. Develop a context-based language management system
5. Ensure all components adapt to language changes

## Implementation Steps

### 1. Setup i18n Infrastructure

- Create i18n configuration (`src/lib/i18n/config.ts`):
  ```typescript
  // Define supported languages
  // Set default language
  // Configure language detection
  ```

- Create translation utility functions (`src/lib/i18n/translate.ts`):
  ```typescript
  // Translation function
  // Pluralization support
  // Interpolation support
  ```

- Set up language detection and persistence (`src/lib/i18n/languageDetection.ts`):
  ```typescript
  // Detect user's preferred language
  // Store language preference
  ```

### 2. Create Translation Files

- English translations (`public/locales/en/common.json`):
  ```json
  // Common UI elements
  // Error messages
  // Form labels
  ```

- English translations for products (`public/locales/en/products.json`):
  ```json
  // Product-related terms
  // Category names
  // Filter labels
  ```

- English translations for orders (`public/locales/en/orders.json`):
  ```json
  // Order status terms
  // Order action buttons
  // Order form labels
  ```

- Arabic translations (`public/locales/ar/common.json`):
  ```json
  // Common UI elements in Arabic
  // Error messages in Arabic
  // Form labels in Arabic
  ```

- Arabic translations for products (`public/locales/ar/products.json`):
  ```json
  // Product-related terms in Arabic
  // Category names in Arabic
  // Filter labels in Arabic
  ```

- Arabic translations for orders (`public/locales/ar/orders.json`):
  ```json
  // Order status terms in Arabic
  // Order action buttons in Arabic
  // Order form labels in Arabic
  ```

### 3. Implement Language Context

- Enhance Language Context (`src/contexts/LanguageContext.tsx`):
  ```typescript
  // Language state management
  // Language switching function
  // Translation function
  // RTL direction detection
  ```

- Create Language Toggle component (`src/components/LanguageToggle.tsx`):
  ```typescript
  // Toggle between English and Arabic
  // Display current language
  // Use flag icons for languages
  ```

### 4. RTL Support Implementation

- Create RTL utility functions (`src/utils/rtl.ts`):
  ```typescript
  // Check if current language is RTL
  // Apply RTL-specific styles
  // Handle RTL-specific layout issues
  ```

- Update root layout for RTL support (`src/app/layout.tsx`):
  ```typescript
  // Set dir attribute based on language
  // Apply RTL-specific styles
  ```

### 5. Component Adaptation

- Update base UI components for i18n support:
  ```typescript
  // Use translation function for all text
  // Handle RTL layout differences
  // Support text expansion in different languages
  ```

- Update layout components for RTL support:
  ```typescript
  // Adjust layout direction based on language
  // Mirror UI elements for RTL
  ```

## Deliverables

1. Complete i18n configuration
2. Translation files for English and Arabic
3. Language Context with switching functionality
4. RTL support for Arabic language
5. Components adapted for i18n and RTL

## Dependencies

- Task 001: Project Setup
- Task 002: Core Architecture
- Task 003: Base UI Components
- Task 004: Layout Components

## Estimated Time
6 hours