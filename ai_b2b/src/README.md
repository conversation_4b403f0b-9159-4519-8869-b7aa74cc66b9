# KAUST B2B Marketplace

A comprehensive B2B marketplace platform for King Abdullah University of Science and Technology, built with Next.js 15, React 19, and TypeScript.

## 🚀 Features Implemented

### ✅ F001 - Basic Layout Structure (Completed)

- **Responsive Layout Components**: Header, Footer, MainContent, PageContainer, PortalLayout
- **KAUST Branding**: Custom color palette inspired by KAUST's official website
- **Mobile-First Design**: Responsive breakpoints and grid system
- **Accessibility**: WCAG 2.1 AA compliant with semantic HTML and keyboard navigation
- **TypeScript Integration**: Fully typed components with proper interfaces
- **Tailwind CSS**: Custom design tokens and utility classes

### 🔄 Next Phase
- **F002 - State Management Foundation**: Redux Toolkit implementation (Planned)

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom KAUST design tokens
- **UI Components**: Custom React components
- **Development**: Turbopack for fast development builds

## 🎨 Design System

### Color Palette
- **Primary**: KAUST Blue (#3b82f6)
- **Secondary**: KAUST Navy (#1e293b)
- **Accent**: KAUST Gold (#f59e0b)
- **Neutral**: Gray scale for backgrounds and text

### Typography
- **Primary Font**: Geist Sans (system fallback)
- **Monospace**: Geist Mono
- **Arabic Support**: Prepared for multi-language implementation

## 📁 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and CSS variables
│   ├── layout.tsx           # Root layout with metadata
│   └── page.tsx             # Homepage implementation
├── components/
│   └── layout/
│       ├── Header.tsx       # Navigation header
│       ├── Footer.tsx       # Site footer
│       ├── MainContent.tsx  # Main content wrapper
│       ├── PageContainer.tsx # Page-level container
│       ├── PortalLayout.tsx # Complete layout composition
│       └── index.ts         # Component exports
└── public/                  # Static assets
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. **Navigate to the project directory**:
   ```bash
   cd src
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

### Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🎯 Implementation Details

### Layout Components

#### Header
- Sticky navigation with KAUST branding
- Responsive design with mobile menu
- Placeholder areas for search and user actions
- Accessibility-compliant navigation

#### Footer
- Multi-column layout with company information
- Quick links and support sections
- Copyright and legal information
- Responsive grid system

#### PortalLayout
- Complete page layout composition
- Flexible header/footer visibility
- Proper semantic HTML structure
- Consistent spacing and typography

#### PageContainer
- Configurable max-width containers
- Optional title and subtitle support
- Consistent padding and margins
- Responsive design patterns

### Styling Architecture

#### Tailwind Configuration
- Custom KAUST color palette
- Extended spacing and typography scales
- Custom animations and transitions
- Responsive breakpoint system

#### CSS Variables
- Light/dark theme support
- Semantic color naming
- Consistent design tokens
- Easy theme customization

## 🔧 Development Guidelines

### Component Standards
- TypeScript interfaces for all props
- Consistent naming conventions
- Proper accessibility attributes
- Responsive design patterns

### Code Quality
- ESLint configuration
- TypeScript strict mode
- Consistent formatting
- Component documentation

## 📱 Responsive Design

### Breakpoints
- `xs`: 475px (extra small devices)
- `sm`: 640px (small devices)
- `md`: 768px (medium devices)
- `lg`: 1024px (large devices)
- `xl`: 1280px (extra large devices)
- `2xl`: 1536px (2x extra large devices)

### Grid System
- 12-column responsive grid
- Flexible container widths
- Mobile-first approach
- Consistent spacing patterns

## ♿ Accessibility Features

- Semantic HTML structure
- ARIA labels and roles
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- Color contrast compliance

## 🔮 Future Enhancements

### Phase 2: State Management Foundation (F002)
- Redux Toolkit implementation
- Persistent storage
- Type-safe state management
- Performance optimization

### Phase 3: Authentication System
- User authentication
- Role-based access control
- Session management
- Security features

### Phase 4: Product Catalog
- Product browsing
- Search and filtering
- Category navigation
- Product details

## 📄 License

Copyright © 2024 King Abdullah University of Science and Technology. All rights reserved.

## 🤝 Contributing

This project follows the KAUST development standards and guidelines. Please ensure all contributions maintain the established code quality and design patterns.

---

**Built with ❤️ for KAUST by the Development Team**
