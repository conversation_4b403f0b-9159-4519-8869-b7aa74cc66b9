'use client';

import { <PERSON>Layout, PageContainer } from '@/components/layout';
import Link from 'next/link';
import { Glass, GlassCard, GlassButton, GlassBadge } from '@/components/ui/Glass';

export default function Home() {
  return (
    <PortalLayout>
      <PageContainer 
        title="Welcome to KAUST B2B Marketplace" 
        subtitle="Your gateway to scientific and technological solutions"
      >
        <div className="space-y-8">
          {/* Hero Section */}
          <Glass intensity="heavy" gradient className="p-8">
            <div className="max-w-3xl">
              <GlassBadge variant="secondary" size="md" className="mb-4">
                ✨ New Glassmorphism Design System
              </GlassBadge>
              <h2 className="text-3xl font-bold mb-4">
                Empowering Innovation Through Collaboration
              </h2>
              <p className="text-lg opacity-90 mb-6">
                Connect with leading suppliers, discover cutting-edge products, and streamline your procurement process with KAUST's comprehensive B2B marketplace featuring our new glassmorphism design system.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <GlassButton variant="primary" size="lg">
                  Explore Products
                </GlassButton>
                <Link href="/theme-showcase">
                  <GlassButton variant="secondary" size="lg">
                    🎨 Theme Showcase
                  </GlassButton>
                </Link>
              </div>
            </div>
          </Glass>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <GlassCard
              intensity="medium"
              color="primary"
              header={
                <div className="flex items-center gap-3">
                  <Glass intensity="heavy" color="primary" className="p-3 rounded-lg">
                    <div className="w-6 h-6 bg-kaust-teal rounded"></div>
                  </Glass>
                  <h3 className="text-lg font-semibold">
                    Product Catalog
                  </h3>
                </div>
              }
            >
              <p className="opacity-80">
                Browse our comprehensive catalog of scientific equipment, research tools, and technological solutions with our new glassmorphism interface.
              </p>
            </GlassCard>

            <GlassCard
              intensity="medium"
              color="secondary"
              header={
                <div className="flex items-center gap-3">
                  <Glass intensity="heavy" color="secondary" className="p-3 rounded-lg">
                    <div className="w-6 h-6 bg-kaust-gold rounded"></div>
                  </Glass>
                  <h3 className="text-lg font-semibold">
                    Order Management
                  </h3>
                </div>
              }
            >
              <p className="opacity-80">
                Streamline your procurement process with our integrated order and quotation management system featuring beautiful glass effects.
              </p>
            </GlassCard>

            <GlassCard
              intensity="medium"
              color="accent"
              header={
                <div className="flex items-center gap-3">
                  <Glass intensity="heavy" color="accent" className="p-3 rounded-lg">
                    <div className="w-6 h-6 bg-kaust-orange rounded"></div>
                  </Glass>
                  <h3 className="text-lg font-semibold">
                    Enterprise Solutions
                  </h3>
                </div>
              }
            >
              <p className="opacity-80">
                Access enterprise-grade features designed for academic and research institutions with dynamic theming support.
              </p>
            </GlassCard>
          </div>

          {/* Status Section */}
          <GlassCard
            intensity="light"
            header={
              <h3 className="text-lg font-semibold">
                Implementation Status
              </h3>
            }
          >
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Basic Layout Structure</span>
                <GlassBadge variant="primary" size="sm">
                  ✓ Completed
                </GlassBadge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Glassmorphism Theme System</span>
                <GlassBadge variant="primary" size="sm">
                  ✓ Completed
                </GlassBadge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">State Management Foundation</span>
                <GlassBadge variant="secondary" size="sm">
                  ⏳ Next Phase
                </GlassBadge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Authentication System</span>
                <GlassBadge variant="neutral" size="sm">
                  📋 Planned
                </GlassBadge>
              </div>
            </div>
          </GlassCard>
        </div>
      </PageContainer>
    </PortalLayout>
  );
}
