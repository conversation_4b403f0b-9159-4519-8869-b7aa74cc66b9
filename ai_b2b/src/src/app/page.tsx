import { PortalLayout, PageContainer } from '@/components/layout';
import Link from 'next/link';

export default function Home() {
  return (
    <PortalLayout>
      <PageContainer 
        title="Welcome to KAUST B2B Marketplace" 
        subtitle="Your gateway to scientific and technological solutions"
      >
        <div className="space-y-8">
          {/* Hero Section */}
          <div className="hero-gradient rounded-lg p-8 text-white shadow-lg">
            <div className="max-w-3xl">
              <h2 className="text-2xl font-bold mb-4">
                Empowering Innovation Through Collaboration
              </h2>
              <p className="text-lg opacity-90 mb-6">
                Connect with leading suppliers, discover cutting-edge products, and streamline your procurement process with KAUST's comprehensive B2B marketplace.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-white text-kaust-teal px-6 py-3 rounded-md font-semibold hover:bg-gray-100 transition-colors shadow-md">
                  Explore Products
                </button>
                <Link href="/theme-showcase">
                  <button className="border-2 border-white text-white px-6 py-3 rounded-md font-semibold hover:bg-white hover:text-kaust-orange transition-colors">
                    🎨 Theme Showcase
                  </button>
                </Link>
              </div>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center mb-4">
                <div className="w-6 h-6 bg-kaust-teal rounded"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Product Catalog
              </h3>
              <p className="text-gray-600">
                Browse our comprehensive catalog of scientific equipment, research tools, and technological solutions.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                <div className="w-6 h-6 bg-kaust-gold rounded"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Order Management
              </h3>
              <p className="text-gray-600">
                Streamline your procurement process with our integrated order and quotation management system.
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                <div className="w-6 h-6 bg-kaust-orange rounded"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Enterprise Solutions
              </h3>
              <p className="text-gray-600">
                Access enterprise-grade features designed for academic and research institutions.
              </p>
            </div>
          </div>

          {/* Status Section */}
          <div className="bg-gray-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Implementation Status
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Basic Layout Structure</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  ✓ Completed
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Glassmorphism Theme System</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  ✓ Completed
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">State Management Foundation</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  ⏳ Next Phase
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Authentication System</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  📋 Planned
                </span>
              </div>
            </div>
          </div>
        </div>
      </PageContainer>
    </PortalLayout>
  );
}
