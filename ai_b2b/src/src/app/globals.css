@import "tailwindcss/preflight";
@import "tailwindcss/theme";
@import "tailwindcss/utilities";

:root {
  /* KAUST Official Brand Colors */
  --kaust-gold: #F9C217;
  --kaust-orange: #EF8721;
  --kaust-teal: #1F9EA8;
  --kaust-grey: #958B83;
  --kaust-blue: #0077b6;
  --kaust-navy: #023e8a;

  /* Enhanced Glassmorphism System */
  --glass-opacity-light: 0.08;
  --glass-opacity-medium: 0.12;
  --glass-opacity-heavy: 0.18;
  --glass-opacity-ultra: 0.25;

  --glass-blur-light: 8px;
  --glass-blur-medium: 16px;
  --glass-blur-heavy: 24px;
  --glass-blur-ultra: 32px;

  --glass-border-opacity: 0.2;
  --glass-shadow-opacity: 0.1;
  --glass-highlight-opacity: 0.3;

  /* Base Glass Variables */
  --glass-bg: rgba(255, 255, 255, var(--glass-opacity-medium));
  --glass-border: rgba(255, 255, 255, var(--glass-border-opacity));
  --glass-shadow: rgba(0, 0, 0, var(--glass-shadow-opacity));
  --glass-backdrop-blur: var(--glass-blur-medium);
  --glass-border-radius: 16px;
  --glass-highlight: rgba(255, 255, 255, var(--glass-highlight-opacity));

  /* KAUST Branded Glass Colors */
  --glass-primary: rgba(31, 158, 168, var(--glass-opacity-medium));
  --glass-secondary: rgba(249, 194, 23, var(--glass-opacity-medium));
  --glass-accent: rgba(239, 135, 33, var(--glass-opacity-medium));
  --glass-neutral: rgba(149, 139, 131, var(--glass-opacity-medium));
  --glass-blue: rgba(0, 119, 182, var(--glass-opacity-medium));
  --glass-navy: rgba(2, 62, 138, var(--glass-opacity-medium));

  /* Light Theme */
  --background: #ffffff;
  --foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #475569;
  --border: #e2e8f0;
  --input: #ffffff;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --primary: var(--kaust-teal);
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --accent: var(--kaust-gold);
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: var(--kaust-teal);
  --radius: 0.5rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --border: #334155;
    --input: #1e293b;
    --card: #0f172a;
    --card-foreground: #ededed;
    --primary: var(--kaust-teal);
    --primary-foreground: #ffffff;
    --secondary: #1e293b;
    --secondary-foreground: #f8fafc;
    --accent: var(--kaust-gold);
    --accent-foreground: #0f172a;
    --destructive: #dc2626;
    --destructive-foreground: #ffffff;
    --ring: var(--kaust-teal);

    /* Dark Theme Glass Adjustments */
    --glass-opacity-light: 0.05;
    --glass-opacity-medium: 0.08;
    --glass-opacity-heavy: 0.12;
    --glass-opacity-ultra: 0.18;

    --glass-border-opacity: 0.15;
    --glass-shadow-opacity: 0.4;
    --glass-highlight-opacity: 0.2;

    --glass-bg: rgba(255, 255, 255, var(--glass-opacity-medium));
    --glass-border: rgba(255, 255, 255, var(--glass-border-opacity));
    --glass-shadow: rgba(0, 0, 0, var(--glass-shadow-opacity));
    --glass-highlight: rgba(255, 255, 255, var(--glass-highlight-opacity));

    --glass-primary: rgba(31, 158, 168, var(--glass-opacity-heavy));
    --glass-secondary: rgba(249, 194, 23, var(--glass-opacity-heavy));
    --glass-accent: rgba(239, 135, 33, var(--glass-opacity-heavy));
    --glass-neutral: rgba(149, 139, 131, var(--glass-opacity-heavy));
    --glass-blue: rgba(0, 119, 182, var(--glass-opacity-heavy));
    --glass-navy: rgba(2, 62, 138, var(--glass-opacity-heavy));
  }
}

* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  scroll-behavior: smooth;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px var(--kaust-teal), 0 0 0 4px rgba(31, 158, 168, 0.2);
  transition: box-shadow 0.2s ease-in-out;
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--kaust-teal), 0 0 0 4px rgba(31, 158, 168, 0.2);
}

.text-muted-foreground {
  color: var(--muted-foreground);
}

@media print {
  .no-print {
    display: none !important;
  }
}

/* KAUST Colors */
.bg-kaust-gold     { background-color: var(--kaust-gold); }
.bg-kaust-orange   { background-color: var(--kaust-orange); }
.bg-kaust-teal     { background-color: var(--kaust-teal); }
.bg-kaust-grey     { background-color: var(--kaust-grey); }

.text-kaust-gold   { color: var(--kaust-gold); }
.text-kaust-orange { color: var(--kaust-orange); }
.text-kaust-teal   { color: var(--kaust-teal); }
.text-kaust-grey   { color: var(--kaust-grey); }

.border-kaust-gold   { border-color: var(--kaust-gold); }
.border-kaust-orange { border-color: var(--kaust-orange); }
.border-kaust-teal   { border-color: var(--kaust-teal); }
.border-kaust-grey   { border-color: var(--kaust-grey); }

  .kaust-gradient {
    background: linear-gradient(135deg, var(--kaust-blue) 0%, var(--kaust-navy) 100%);
  }

  .kaust-text-gradient {
    background: linear-gradient(135deg, var(--kaust-blue) 0%, var(--kaust-navy) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .hero-gradient {
    background: linear-gradient(135deg, var(--kaust-teal) 0%, var(--kaust-orange) 100%);
  }

  /* Enhanced Glassmorphism Base */
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--glass-backdrop-blur));
    -webkit-backdrop-filter: blur(var(--glass-backdrop-blur));
    border: 1px solid var(--glass-border);
    border-radius: var(--glass-border-radius);
    box-shadow:
      0 8px 32px var(--glass-shadow),
      inset 0 1px 0 var(--glass-highlight);
    position: relative;
    overflow: hidden;
  }

  .glass::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; height: 1px;
    background: linear-gradient(90deg, transparent, var(--glass-highlight), transparent);
    opacity: 0.6;
  }

  .glass::after {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 50%,
      rgba(0, 0, 0, 0.05) 100%);
    pointer-events: none;
  }

  /* Enhanced Glass Color Variants */
  .glass-primary {
    background: var(--glass-primary);
    border-color: rgba(31,158,168, var(--glass-border-opacity));
    box-shadow: 0 8px 32px rgba(31,158,168, 0.15), inset 0 1px 0 rgba(31,158,168, 0.3);
  }
  .glass-secondary {
    background: var(--glass-secondary);
    border-color: rgba(249,194,23, var(--glass-border-opacity));
    box-shadow: 0 8px 32px rgba(249,194,23, 0.15), inset 0 1px 0 rgba(249,194,23, 0.3);
  }
  .glass-accent {
    background: var(--glass-accent);
    border-color: rgba(239,135,33, var(--glass-border-opacity));
    box-shadow: 0 8px 32px rgba(239,135,33, 0.15), inset 0 1px 0 rgba(239,135,33, 0.3);
  }
  .glass-neutral {
    background: var(--glass-neutral);
    border-color: rgba(149,139,131, var(--glass-border-opacity));
    box-shadow: 0 8px 32px rgba(149,139,131, 0.15), inset 0 1px 0 rgba(149,139,131, 0.3);
  }
  .glass-blue {
    background: var(--glass-blue);
    border-color: rgba(0,119,182, var(--glass-border-opacity));
    box-shadow: 0 8px 32px rgba(0,119,182, 0.15), inset 0 1px 0 rgba(0,119,182, 0.3);
  }
  .glass-navy {
    background: var(--glass-navy);
    border-color: rgba(2,62,138, var(--glass-border-opacity));
    box-shadow: 0 8px 32px rgba(2,62,138, 0.15), inset 0 1px 0 rgba(2,62,138, 0.3);
  }

  /* Glass Intensity Variants */
  .glass-light {
    background: rgba(255, 255, 255, var(--glass-opacity-light));
    backdrop-filter: blur(var(--glass-blur-light));
    -webkit-backdrop-filter: blur(var(--glass-blur-light));
    box-shadow: 0 4px 16px var(--glass-shadow);
  }

  .glass-medium {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--glass-blur-medium));
    -webkit-backdrop-filter: blur(var(--glass-blur-medium));
  }

  .glass-heavy {
    background: rgba(255, 255, 255, var(--glass-opacity-heavy));
    backdrop-filter: blur(var(--glass-blur-heavy));
    -webkit-backdrop-filter: blur(var(--glass-blur-heavy));
    box-shadow: 0 12px 48px var(--glass-shadow);
  }

  .glass-ultra {
    background: rgba(255, 255, 255, var(--glass-opacity-ultra));
    backdrop-filter: blur(var(--glass-blur-ultra));
    -webkit-backdrop-filter: blur(var(--glass-blur-ultra));
    box-shadow: 0 16px 64px var(--glass-shadow);
  }

  .glass-interactive {
    transition: all 0.3s ease-in-out;
    cursor: pointer;
  }

  .glass-interactive:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 12px 40px var(--glass-shadow);
    transform: translateY(-2px);
  }

  .glass-interactive:active {
    transform: translateY(0);
    box-shadow: 0 4px 16px var(--glass-shadow);
  }

  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--glass-backdrop-blur));
    -webkit-backdrop-filter: blur(var(--glass-backdrop-blur));
    border: 1px solid var(--glass-border);
    border-radius: var(--glass-border-radius);
    box-shadow: 0 8px 32px var(--glass-shadow);
    position: relative;
    overflow: hidden;
    padding: 1.5rem;
  }

  .glass-card-sm {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--glass-backdrop-blur));
    -webkit-backdrop-filter: blur(var(--glass-backdrop-blur));
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    box-shadow: 0 8px 32px var(--glass-shadow);
    position: relative;
    overflow: hidden;
    padding: 1rem;
  }

  .glass-card-lg {
    background: var(--glass-bg);
    backdrop-filter: blur(var(--glass-backdrop-blur));
    -webkit-backdrop-filter: blur(var(--glass-backdrop-blur));
    border: 1px solid var(--glass-border);
    border-radius: 24px;
    box-shadow: 0 8px 32px var(--glass-shadow);
    position: relative;
    overflow: hidden;
    padding: 2rem;
  }

  /* Enhanced Theme Variants */
  [data-theme="glass-light"] {
    --glass-opacity-light: 0.08;
    --glass-opacity-medium: 0.12;
    --glass-opacity-heavy: 0.18;
    --glass-border-opacity: 0.25;
    --glass-shadow-opacity: 0.08;
    --glass-highlight-opacity: 0.4;

    --glass-bg: rgba(255, 255, 255, var(--glass-opacity-medium));
    --glass-border: rgba(255, 255, 255, var(--glass-border-opacity));
    --glass-shadow: rgba(0, 0, 0, var(--glass-shadow-opacity));
    --glass-highlight: rgba(255, 255, 255, var(--glass-highlight-opacity));

    --background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  }

  [data-theme="glass-dark"] {
    --glass-opacity-light: 0.04;
    --glass-opacity-medium: 0.08;
    --glass-opacity-heavy: 0.15;
    --glass-border-opacity: 0.12;
    --glass-shadow-opacity: 0.5;
    --glass-highlight-opacity: 0.15;

    --glass-bg: rgba(255, 255, 255, var(--glass-opacity-medium));
    --glass-border: rgba(255, 255, 255, var(--glass-border-opacity));
    --glass-shadow: rgba(0, 0, 0, var(--glass-shadow-opacity));
    --glass-highlight: rgba(255, 255, 255, var(--glass-highlight-opacity));

    --background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }

  [data-theme="glass-kaust"] {
    --glass-opacity-light: 0.1;
    --glass-opacity-medium: 0.15;
    --glass-opacity-heavy: 0.22;
    --glass-border-opacity: 0.3;
    --glass-shadow-opacity: 0.15;
    --glass-highlight-opacity: 0.35;

    --glass-bg: rgba(31, 158, 168, var(--glass-opacity-medium));
    --glass-border: rgba(31, 158, 168, var(--glass-border-opacity));
    --glass-shadow: rgba(31, 158, 168, var(--glass-shadow-opacity));
    --glass-highlight: rgba(249, 194, 23, var(--glass-highlight-opacity));

    --background: linear-gradient(135deg, var(--kaust-teal) 0%, var(--kaust-blue) 50%, var(--kaust-navy) 100%);
    --primary: var(--kaust-gold);
    --accent: var(--kaust-orange);
  }

  [data-theme="glass-kaust-warm"] {
    --glass-opacity-light: 0.12;
    --glass-opacity-medium: 0.18;
    --glass-opacity-heavy: 0.25;
    --glass-border-opacity: 0.35;
    --glass-shadow-opacity: 0.12;
    --glass-highlight-opacity: 0.4;

    --glass-bg: rgba(249, 194, 23, var(--glass-opacity-medium));
    --glass-border: rgba(249, 194, 23, var(--glass-border-opacity));
    --glass-shadow: rgba(239, 135, 33, var(--glass-shadow-opacity));
    --glass-highlight: rgba(31, 158, 168, var(--glass-highlight-opacity));

    --background: linear-gradient(135deg, var(--kaust-gold) 0%, var(--kaust-orange) 100%);
    --primary: var(--kaust-teal);
    --accent: var(--kaust-blue);
  }

  [data-theme="glass-kaust-cool"] {
    --glass-opacity-light: 0.08;
    --glass-opacity-medium: 0.14;
    --glass-opacity-heavy: 0.20;
    --glass-border-opacity: 0.25;
    --glass-shadow-opacity: 0.18;
    --glass-highlight-opacity: 0.3;

    --glass-bg: rgba(0, 119, 182, var(--glass-opacity-medium));
    --glass-border: rgba(0, 119, 182, var(--glass-border-opacity));
    --glass-shadow: rgba(2, 62, 138, var(--glass-shadow-opacity));
    --glass-highlight: rgba(31, 158, 168, var(--glass-highlight-opacity));

    --background: linear-gradient(135deg, var(--kaust-blue) 0%, var(--kaust-navy) 100%);
    --primary: var(--kaust-teal);
    --accent: var(--kaust-gold);
  }

  /* Normal Theme Variants */
  [data-theme="normal-light"] {
    --background: #ffffff;
    --foreground: #0f172a;
    --muted: #f8fafc;
    --muted-foreground: #475569;
    --border: #e2e8f0;
    --input: #ffffff;
    --card: #ffffff;
    --card-foreground: #0f172a;
    --primary: var(--kaust-teal);
    --primary-foreground: #ffffff;
    --secondary: #f1f5f9;
    --secondary-foreground: #0f172a;
    --accent: var(--kaust-gold);
    --accent-foreground: #0f172a;

    /* Disable glass effects for normal theme */
    --glass-bg: #ffffff;
    --glass-border: #e2e8f0;
    --glass-shadow: rgba(0, 0, 0, 0.1);
    --glass-backdrop-blur: 0px;
    --glass-highlight: #ffffff;
  }

  [data-theme="normal-dark"] {
    --background: #0f172a;
    --foreground: #ededed;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --border: #334155;
    --input: #1e293b;
    --card: #1e293b;
    --card-foreground: #ededed;
    --primary: var(--kaust-teal);
    --primary-foreground: #ffffff;
    --secondary: #1e293b;
    --secondary-foreground: #f8fafc;
    --accent: var(--kaust-gold);
    --accent-foreground: #0f172a;

    /* Disable glass effects for normal theme */
    --glass-bg: #1e293b;
    --glass-border: #334155;
    --glass-shadow: rgba(0, 0, 0, 0.3);
    --glass-backdrop-blur: 0px;
    --glass-highlight: #334155;
  }

  /* Normal theme specific styles */
  [data-theme="normal-light"] .glass,
  [data-theme="normal-light"] .glass-light,
  [data-theme="normal-light"] .glass-medium,
  [data-theme="normal-light"] .glass-heavy,
  [data-theme="normal-light"] .glass-ultra {
    background: var(--card);
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    border: 1px solid var(--border);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  [data-theme="normal-dark"] .glass,
  [data-theme="normal-dark"] .glass-light,
  [data-theme="normal-dark"] .glass-medium,
  [data-theme="normal-dark"] .glass-heavy,
  [data-theme="normal-dark"] .glass-ultra {
    background: var(--card);
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    border: 1px solid var(--border);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }

  .glass-bg-gradient-primary {
    background: linear-gradient(135deg, rgba(31, 158, 168, 0.1), rgba(249, 194, 23, 0.1));
  }

  .glass-bg-gradient-warm {
    background: linear-gradient(135deg, rgba(239, 135, 33, 0.1), rgba(249, 194, 23, 0.1));
  }

  .glass-bg-gradient-cool {
    background: linear-gradient(135deg, rgba(31, 158, 168, 0.1), rgba(149, 139, 131, 0.1));
  }

  @media (prefers-reduced-motion: reduce) {
    .glass-interactive {
      transition: none;
    }
  }

  @media (max-width: 768px) {
    .glass {
      backdrop-filter: blur(10px);
    }
  }

  @media (max-width: 480px) {
    .glass-card {
      padding: 1rem;
      border-radius: 12px;
    }
    .glass-card-lg {
      padding: 1.5rem;
      border-radius: 16px;
    }
  }
