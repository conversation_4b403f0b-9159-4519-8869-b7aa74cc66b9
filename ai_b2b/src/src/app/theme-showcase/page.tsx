'use client';

import React, { useState } from 'react';
import { ThemeProvider, ThemeSelector, useTheme } from '@/components/theme/ThemeProvider';
import { 
  Glass, 
  GlassCard, 
  GlassButton, 
  GlassInput, 
  GlassModal, 
  GlassNav, 
  GlassBadge 
} from '@/components/ui/Glass';

function ThemeShowcaseContent() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { themeConfig, isKaustTheme } = useTheme();

  return (
    <div className="min-h-screen p-6 space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold kaust-text-gradient">
          KAUST Glassmorphism Theme System
        </h1>
        <p className="text-lg opacity-80 max-w-2xl mx-auto">
          Experience our dynamic glassmorphism design system with KAUST branding integration. 
          Switch between themes to see the beautiful glass effects adapt to different color schemes.
        </p>
        
        {/* KAUST Logo Representation */}
        <div className="flex justify-center my-8">
          <Glass intensity="heavy" color="primary" className="p-6 rounded-full">
            <div className="w-16 h-16 flex items-center justify-center">
              <div className="grid grid-cols-2 gap-1">
                <div className="w-6 h-6 bg-kaust-gold rounded-full"></div>
                <div className="w-6 h-6 bg-kaust-orange rounded-full"></div>
                <div className="w-6 h-6 bg-kaust-teal rounded-full"></div>
                <div className="w-6 h-6 bg-kaust-grey rounded-full"></div>
              </div>
            </div>
          </Glass>
        </div>
      </div>

      {/* Theme Selector */}
      <div className="flex justify-center">
        <ThemeSelector />
      </div>

      {/* Current Theme Info */}
      <GlassCard 
        intensity="medium" 
        color="primary"
        header={
          <div className="flex items-center gap-3">
            <span className="text-2xl">{themeConfig.icon}</span>
            <div>
              <h3 className="text-lg font-semibold">{themeConfig.displayName}</h3>
              <p className="text-sm opacity-70">{themeConfig.description}</p>
            </div>
          </div>
        }
      >
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <div className="text-xs font-medium opacity-60">PRIMARY</div>
            <div 
              className="w-full h-8 rounded-lg border border-white/20"
              style={{ backgroundColor: themeConfig.colors.primary }}
            ></div>
            <div className="text-xs font-mono">{themeConfig.colors.primary}</div>
          </div>
          <div className="space-y-2">
            <div className="text-xs font-medium opacity-60">SECONDARY</div>
            <div 
              className="w-full h-8 rounded-lg border border-white/20"
              style={{ backgroundColor: themeConfig.colors.secondary }}
            ></div>
            <div className="text-xs font-mono">{themeConfig.colors.secondary}</div>
          </div>
          <div className="space-y-2">
            <div className="text-xs font-medium opacity-60">ACCENT</div>
            <div 
              className="w-full h-8 rounded-lg border border-white/20"
              style={{ backgroundColor: themeConfig.colors.accent }}
            ></div>
            <div className="text-xs font-mono">{themeConfig.colors.accent}</div>
          </div>
          <div className="space-y-2">
            <div className="text-xs font-medium opacity-60">BACKGROUND</div>
            <div 
              className="w-full h-8 rounded-lg border border-white/20"
              style={{ background: themeConfig.colors.background }}
            ></div>
            <div className="text-xs font-mono">Gradient</div>
          </div>
        </div>
      </GlassCard>

      {/* Glass Intensity Showcase */}
      <GlassCard header={<h3 className="text-xl font-semibold">Glass Intensity Levels</h3>}>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {(['light', 'medium', 'heavy', 'ultra'] as const).map((intensity) => (
            <Glass key={intensity} intensity={intensity} className="p-4 text-center">
              <div className="text-sm font-medium mb-2 capitalize">{intensity}</div>
              <div className="text-xs opacity-70">
                {intensity === 'light' && 'Subtle transparency'}
                {intensity === 'medium' && 'Balanced effect'}
                {intensity === 'heavy' && 'Strong glass look'}
                {intensity === 'ultra' && 'Maximum depth'}
              </div>
            </Glass>
          ))}
        </div>
      </GlassCard>

      {/* Glass Color Variants */}
      <GlassCard header={<h3 className="text-xl font-semibold">KAUST Color Variants</h3>}>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {(['primary', 'secondary', 'accent', 'neutral', 'blue', 'navy'] as const).map((color) => (
            <Glass key={color} color={color} className="p-4 text-center">
              <div className="text-sm font-medium mb-2 capitalize">{color}</div>
              <GlassBadge variant={color} size="sm">
                {color}
              </GlassBadge>
            </Glass>
          ))}
        </div>
      </GlassCard>

      {/* Interactive Components */}
      <GlassCard header={<h3 className="text-xl font-semibold">Interactive Components</h3>}>
        <div className="space-y-6">
          {/* Buttons */}
          <div>
            <h4 className="text-lg font-medium mb-3">Glass Buttons</h4>
            <div className="flex flex-wrap gap-3">
              <GlassButton variant="default">Default</GlassButton>
              <GlassButton variant="primary">Primary</GlassButton>
              <GlassButton variant="secondary">Secondary</GlassButton>
              <GlassButton variant="accent">Accent</GlassButton>
              <GlassButton 
                variant="primary" 
                onClick={() => setIsModalOpen(true)}
              >
                Open Modal
              </GlassButton>
            </div>
          </div>

          {/* Inputs */}
          <div>
            <h4 className="text-lg font-medium mb-3">Glass Inputs</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
              <GlassInput placeholder="Light intensity" intensity="light" />
              <GlassInput placeholder="Medium intensity" intensity="medium" />
              <GlassInput placeholder="Heavy intensity" intensity="heavy" />
              <GlassInput placeholder="Ultra intensity" intensity="ultra" />
            </div>
          </div>

          {/* Badges */}
          <div>
            <h4 className="text-lg font-medium mb-3">Glass Badges</h4>
            <div className="flex flex-wrap gap-2">
              <GlassBadge variant="primary">New</GlassBadge>
              <GlassBadge variant="secondary">Featured</GlassBadge>
              <GlassBadge variant="accent">Hot</GlassBadge>
              <GlassBadge variant="blue">Info</GlassBadge>
              <GlassBadge variant="navy">Premium</GlassBadge>
            </div>
          </div>
        </div>
      </GlassCard>

      {/* Layout Examples */}
      <GlassCard header={<h3 className="text-xl font-semibold">Layout Examples</h3>}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Card Example */}
          <GlassCard 
            intensity="light"
            header={<div className="font-medium">Product Card</div>}
            footer={<GlassButton variant="primary" size="sm">Add to Cart</GlassButton>}
          >
            <div className="space-y-2">
              <div className="text-sm opacity-80">KAUST Research Equipment</div>
              <div className="text-lg font-semibold">Advanced Microscope</div>
              <div className="text-xl font-bold text-kaust-gold">$15,999</div>
            </div>
          </GlassCard>

          {/* Stats Card */}
          <Glass intensity="medium" color="blue" className="p-4">
            <div className="text-center space-y-2">
              <div className="text-2xl font-bold">1,247</div>
              <div className="text-sm opacity-80">Active Users</div>
              <div className="text-xs text-green-400">↗ +12% this month</div>
            </div>
          </Glass>

          {/* Feature Card */}
          <Glass intensity="heavy" gradient className="p-4">
            <div className="space-y-3">
              <div className="text-lg font-semibold">🔬 Research Tools</div>
              <div className="text-sm opacity-80">
                Access cutting-edge laboratory equipment and research facilities.
              </div>
              <GlassButton variant="accent" size="sm">Learn More</GlassButton>
            </div>
          </Glass>
        </div>
      </GlassCard>

      {/* Modal */}
      <GlassModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)}
        size="md"
      >
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Glass Modal Example</h3>
          <p className="opacity-80">
            This modal demonstrates the glassmorphism effect with backdrop blur and 
            transparency. The modal adapts to the current theme automatically.
          </p>
          <div className="flex gap-3 justify-end">
            <GlassButton 
              variant="default" 
              onClick={() => setIsModalOpen(false)}
            >
              Cancel
            </GlassButton>
            <GlassButton 
              variant="primary"
              onClick={() => setIsModalOpen(false)}
            >
              Confirm
            </GlassButton>
          </div>
        </div>
      </GlassModal>

      {/* Footer */}
      <div className="text-center py-8">
        <Glass intensity="light" className="inline-block px-6 py-3">
          <div className="text-sm opacity-70">
            KAUST B2B Marketplace • Glassmorphism Theme System
          </div>
        </Glass>
      </div>
    </div>
  );
}

export default function ThemeShowcasePage() {
  return (
    <ThemeProvider defaultTheme="glass-kaust" enableAutoDetection={false}>
      <ThemeShowcaseContent />
    </ThemeProvider>
  );
}
