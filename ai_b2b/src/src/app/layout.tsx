import type { Metadata, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

export const metadata: Metadata = {
  title: {
    default: "KAUST B2B Marketplace",
    template: "%s | KAUST B2B Marketplace",
  },
  description: "King Abdullah University of Science and Technology B2B Marketplace - Your gateway to scientific and technological solutions",
  keywords: ["KAUST", "B2B", "marketplace", "science", "technology", "university"],
  authors: [{ name: "KAUST" }],
  creator: "KAUST",
  publisher: "KAUST",
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://marketplace.kaust.edu.sa",
    title: "KAUST B2B Marketplace",
    description: "King <PERSON> University of Science and Technology B2B Marketplace",
    siteName: "KAUST B2B Marketplace",
  },
  twitter: {
    card: "summary_large_image",
    title: "KAUST B2B Marketplace",
    description: "King Abdullah University of Science and Technology B2B Marketplace",
  },

};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased h-full`}
        suppressHydrationWarning={true}
      >
        {children}
      </body>
    </html>
  );
}
