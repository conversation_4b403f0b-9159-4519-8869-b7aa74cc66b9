import type { Metada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import { ThemeProvider } from "@/components/theme/ThemeProvider";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

export const metadata: Metadata = {
  title: {
    default: "KAUST B2B Marketplace",
    template: "%s | KAUST B2B Marketplace",
  },
  description: "King Abdullah University of Science and Technology B2B Marketplace - Your gateway to scientific and technological solutions",
  keywords: ["KAUST", "B2B", "marketplace", "science", "technology", "university"],
  authors: [{ name: "<PERSON>AUS<PERSON>" }],
  creator: "<PERSON>A<PERSON><PERSON>",
  publisher: "KAUST",
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://marketplace.kaust.edu.sa",
    title: "KAUST B2B Marketplace",
    description: "King Abdullah University of Science and Technology B2B Marketplace",
    siteName: "KAUST B2B Marketplace",
  },
  twitter: {
    card: "summary_large_image",
    title: "KAUST B2B Marketplace",
    description: "King Abdullah University of Science and Technology B2B Marketplace",
  },

};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased h-full`}
        suppressHydrationWarning={true}
      >
        <ThemeProvider defaultTheme="glass-kaust" enableAutoDetection={true}>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
