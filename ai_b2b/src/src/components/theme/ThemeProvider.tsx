'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

export type GlassTheme =
  | 'glass-light'
  | 'glass-dark'
  | 'glass-kaust'
  | 'glass-kaust-warm'
  | 'glass-kaust-cool';

interface ThemeContextType {
  theme: GlassTheme;
  setTheme: (theme: GlassTheme) => void;
  effectiveTheme: GlassTheme;
  toggleTheme: () => void;
  isKaustTheme: boolean;
  themeConfig: ThemeConfig;
}

interface ThemeConfig {
  name: string;
  displayName: string;
  description: string;
  icon: string;
  category: 'light' | 'dark' | 'kaust';
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
  };
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: GlassTheme;
  enableAutoDetection?: boolean;
  customerId?: string;
}

const THEME_CONFIGS: Record<GlassTheme, ThemeConfig> = {
  'glass-light': {
    name: 'glass-light',
    displayName: 'Light Glass',
    description: 'Clean and bright glassmorphism with subtle transparency',
    icon: '☀️',
    category: 'light',
    colors: {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#EF8721',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
    },
  },
  'glass-dark': {
    name: 'glass-dark',
    displayName: 'Dark Glass',
    description: 'Sophisticated dark glassmorphism with enhanced depth',
    icon: '🌙',
    category: 'dark',
    colors: {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#EF8721',
      background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
    },
  },
  'glass-kaust': {
    name: 'glass-kaust',
    displayName: 'KAUST Glass',
    description: 'Official KAUST branding with teal and gold accents',
    icon: '🏛️',
    category: 'kaust',
    colors: {
      primary: '#F9C217',
      secondary: '#1F9EA8',
      accent: '#EF8721',
      background: 'linear-gradient(135deg, #1F9EA8 0%, #0077b6 50%, #023e8a 100%)',
    },
  },
  'glass-kaust-warm': {
    name: 'glass-kaust-warm',
    displayName: 'KAUST Warm',
    description: 'Warm KAUST theme with gold and orange prominence',
    icon: '🔥',
    category: 'kaust',
    colors: {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#0077b6',
      background: 'linear-gradient(135deg, #F9C217 0%, #EF8721 100%)',
    },
  },
  'glass-kaust-cool': {
    name: 'glass-kaust-cool',
    displayName: 'KAUST Cool',
    description: 'Cool KAUST theme with blue and navy prominence',
    icon: '❄️',
    category: 'kaust',
    colors: {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#F9C217',
      background: 'linear-gradient(135deg, #0077b6 0%, #023e8a 100%)',
    },
  },
};

export function ThemeProvider({
  children,
  defaultTheme = 'glass-light',
  enableAutoDetection = true,
  customerId = 'kaust'
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<GlassTheme>(defaultTheme);
  const [effectiveTheme, setEffectiveTheme] = useState<GlassTheme>(defaultTheme);

  useEffect(() => {
    // Load saved theme from localStorage
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('glass-theme') as GlassTheme;
      if (savedTheme && THEME_CONFIGS[savedTheme]) {
        setTheme(savedTheme);
      } else if (enableAutoDetection) {
        // Auto-detect based on system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        const autoTheme = prefersDark ? 'glass-dark' : 'glass-light';
        setTheme(autoTheme);
      }
    }
  }, [enableAutoDetection]);

  useEffect(() => {
    // Apply theme to document
    const root = document.documentElement;
    root.setAttribute('data-theme', theme);
    setEffectiveTheme(theme);

    // Save to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('glass-theme', theme);
    }

    // Apply background gradient to body
    const themeConfig = THEME_CONFIGS[theme];
    if (themeConfig.colors.background.startsWith('linear-gradient')) {
      document.body.style.background = themeConfig.colors.background;
    }
  }, [theme]);

  const toggleTheme = () => {
    const themes: GlassTheme[] = ['glass-light', 'glass-dark', 'glass-kaust'];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  const isKaustTheme = theme.startsWith('glass-kaust');
  const themeConfig = THEME_CONFIGS[theme];

  const value = {
    theme,
    setTheme,
    effectiveTheme,
    toggleTheme,
    isKaustTheme,
    themeConfig,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Enhanced Theme Selector Component
export function ThemeSelector() {
  const { theme, setTheme, effectiveTheme, toggleTheme, themeConfig } = useTheme();

  const allThemes = Object.values(THEME_CONFIGS);
  const groupedThemes = {
    light: allThemes.filter(t => t.category === 'light'),
    dark: allThemes.filter(t => t.category === 'dark'),
    kaust: allThemes.filter(t => t.category === 'kaust'),
  };

  return (
    <div className="glass-card-sm">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">
            Current Theme
          </span>
          <button
            onClick={toggleTheme}
            className="glass-light px-2 py-1 text-xs rounded-md hover:glass-medium transition-all"
            title="Quick toggle between main themes"
          >
            🔄 Toggle
          </button>
        </div>

        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <span className="text-lg">{themeConfig.icon}</span>
            <div>
              <div className="text-sm font-medium">{themeConfig.displayName}</div>
              <div className="text-xs opacity-70">{themeConfig.description}</div>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          {Object.entries(groupedThemes).map(([category, themes]) => (
            <div key={category}>
              <div className="text-xs font-medium opacity-60 uppercase tracking-wide mb-1">
                {category}
              </div>
              <div className="grid grid-cols-1 gap-1">
                {themes.map((t) => (
                  <button
                    key={t.name}
                    onClick={() => setTheme(t.name as GlassTheme)}
                    className={`
                      flex items-center gap-2 px-2 py-1 text-xs rounded-md transition-all text-left
                      ${theme === t.name
                        ? 'glass-primary ring-1 ring-current'
                        : 'glass-light hover:glass-medium'
                      }
                    `}
                  >
                    <span>{t.icon}</span>
                    <span>{t.displayName}</span>
                    {theme === t.name && <span className="ml-auto">✓</span>}
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
