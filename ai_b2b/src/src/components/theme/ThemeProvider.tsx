'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

export type GlassTheme = 'glass-light' | 'glass-dark' | 'glass-kaust';

interface ThemeContextType {
  theme: GlassTheme;
  setTheme: (theme: GlassTheme) => void;
  effectiveTheme: GlassTheme;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: GlassTheme;
}

export function ThemeProvider({ children, defaultTheme = 'glass-light' }: ThemeProviderProps) {
  const [theme, setTheme] = useState<GlassTheme>(defaultTheme);
  const [effectiveTheme, setEffectiveTheme] = useState<GlassTheme>(defaultTheme);

  useEffect(() => {
    // Apply theme to document
    const root = document.documentElement;
    root.setAttribute('data-theme', theme);
    setEffectiveTheme(theme);
  }, [theme]);

  const value = {
    theme,
    setTheme,
    effectiveTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Theme selector component
export function ThemeSelector() {
  const { theme, setTheme, effectiveTheme } = useTheme();

  const themes = [
    { value: 'glass-light' as const, label: 'Light Glass', icon: '☀️' },
    { value: 'glass-dark' as const, label: 'Dark Glass', icon: '🌙' },
    { value: 'glass-kaust' as const, label: 'KAUST Glass', icon: '🏛️' },
  ];

  return (
    <div className="glass-card-sm">
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Theme:
        </span>
        <select
          value={theme}
          onChange={(e) => setTheme(e.target.value as GlassTheme)}
          className="glass-light px-3 py-1 text-sm rounded-lg border-0 focus-ring bg-transparent"
        >
          {themes.map((t) => (
            <option key={t.value} value={t.value}>
              {t.icon} {t.label}
            </option>
          ))}
        </select>
        <div className="text-xs text-gray-500">
          Active: {effectiveTheme.replace('glass-', '')}
        </div>
      </div>
    </div>
  );
}
