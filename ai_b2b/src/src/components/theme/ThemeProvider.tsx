'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

export type GlassTheme =
  | 'glass-light'
  | 'glass-dark'
  | 'glass-kaust'
  | 'glass-kaust-warm'
  | 'glass-kaust-cool'
  | 'normal-light'
  | 'normal-dark';

interface ThemeContextType {
  theme: GlassTheme;
  setTheme: (theme: GlassTheme) => void;
  effectiveTheme: GlassTheme;
  toggleTheme: () => void;
  toggleLightDark: () => void;
  toggleGlassNormal: () => void;
  isKaustTheme: boolean;
  isGlassTheme: boolean;
  isDarkTheme: boolean;
  themeConfig: ThemeConfig;
}

interface ThemeConfig {
  name: string;
  displayName: string;
  description: string;
  icon: string;
  category: 'light' | 'dark' | 'kaust';
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
  };
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: GlassTheme;
  enableAutoDetection?: boolean;
  customerId?: string;
}

const THEME_CONFIGS: Record<GlassTheme, ThemeConfig> = {
  'glass-light': {
    name: 'glass-light',
    displayName: 'Light Glass',
    description: 'Clean and bright glassmorphism with subtle transparency',
    icon: '☀️',
    category: 'light',
    colors: {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#EF8721',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
    },
  },
  'glass-dark': {
    name: 'glass-dark',
    displayName: 'Dark Glass',
    description: 'Sophisticated dark glassmorphism with enhanced depth',
    icon: '🌙',
    category: 'dark',
    colors: {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#EF8721',
      background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
    },
  },
  'glass-kaust': {
    name: 'glass-kaust',
    displayName: 'KAUST Glass',
    description: 'Official KAUST branding with teal and gold accents',
    icon: '🏛️',
    category: 'kaust',
    colors: {
      primary: '#F9C217',
      secondary: '#1F9EA8',
      accent: '#EF8721',
      background: 'linear-gradient(135deg, #1F9EA8 0%, #0077b6 50%, #023e8a 100%)',
    },
  },
  'glass-kaust-warm': {
    name: 'glass-kaust-warm',
    displayName: 'KAUST Warm',
    description: 'Warm KAUST theme with gold and orange prominence',
    icon: '🔥',
    category: 'kaust',
    colors: {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#0077b6',
      background: 'linear-gradient(135deg, #F9C217 0%, #EF8721 100%)',
    },
  },
  'glass-kaust-cool': {
    name: 'glass-kaust-cool',
    displayName: 'KAUST Cool',
    description: 'Cool KAUST theme with blue and navy prominence',
    icon: '❄️',
    category: 'kaust',
    colors: {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#F9C217',
      background: 'linear-gradient(135deg, #0077b6 0%, #023e8a 100%)',
    },
  },
  'normal-light': {
    name: 'normal-light',
    displayName: 'Light Normal',
    description: 'Clean light theme without glass effects',
    icon: '☀️',
    category: 'light',
    colors: {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#EF8721',
      background: '#ffffff',
    },
  },
  'normal-dark': {
    name: 'normal-dark',
    displayName: 'Dark Normal',
    description: 'Dark theme without glass effects',
    icon: '🌙',
    category: 'dark',
    colors: {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#EF8721',
      background: '#0f172a',
    },
  },
};

export function ThemeProvider({
  children,
  defaultTheme = 'glass-light',
  enableAutoDetection = true,
  customerId = 'kaust'
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<GlassTheme>(defaultTheme);
  const [effectiveTheme, setEffectiveTheme] = useState<GlassTheme>(defaultTheme);

  useEffect(() => {
    // Load saved theme from localStorage
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('glass-theme') as GlassTheme;
      if (savedTheme && THEME_CONFIGS[savedTheme]) {
        setTheme(savedTheme);
      } else if (enableAutoDetection) {
        // Auto-detect based on system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        const autoTheme = prefersDark ? 'glass-dark' : 'glass-light';
        setTheme(autoTheme);
      }
    }
  }, [enableAutoDetection]);

  useEffect(() => {
    // Apply theme to document
    const root = document.documentElement;
    root.setAttribute('data-theme', theme);
    setEffectiveTheme(theme);

    // Save to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('glass-theme', theme);
    }

    // Apply background gradient to body
    const themeConfig = THEME_CONFIGS[theme];
    if (themeConfig.colors.background.startsWith('linear-gradient')) {
      document.body.style.background = themeConfig.colors.background;
    }
  }, [theme]);

  const toggleTheme = () => {
    const themes: GlassTheme[] = ['glass-light', 'glass-dark', 'glass-kaust'];
    const currentIndex = themes.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  const toggleLightDark = () => {
    const isCurrentlyDark = theme.includes('dark');
    const isGlass = theme.startsWith('glass-');

    if (isGlass) {
      if (theme.startsWith('glass-kaust')) {
        // Keep KAUST theme, just toggle light/dark
        setTheme('glass-kaust');
      } else {
        setTheme(isCurrentlyDark ? 'glass-light' : 'glass-dark');
      }
    } else {
      setTheme(isCurrentlyDark ? 'normal-light' : 'normal-dark');
    }
  };

  const toggleGlassNormal = () => {
    const isCurrentlyGlass = theme.startsWith('glass-');
    const isCurrentlyDark = theme.includes('dark');

    if (isCurrentlyGlass) {
      // Switch to normal theme
      setTheme(isCurrentlyDark ? 'normal-dark' : 'normal-light');
    } else {
      // Switch to glass theme
      setTheme(isCurrentlyDark ? 'glass-dark' : 'glass-light');
    }
  };

  const isKaustTheme = theme.startsWith('glass-kaust');
  const isGlassTheme = theme.startsWith('glass-');
  const isDarkTheme = theme.includes('dark');
  const themeConfig = THEME_CONFIGS[theme];

  const value = {
    theme,
    setTheme,
    effectiveTheme,
    toggleTheme,
    toggleLightDark,
    toggleGlassNormal,
    isKaustTheme,
    isGlassTheme,
    isDarkTheme,
    themeConfig,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Light/Dark Toggle Button
export function LightDarkToggle() {
  const { toggleLightDark, isDarkTheme } = useTheme();

  return (
    <button
      onClick={toggleLightDark}
      className="glass-light p-2 rounded-lg hover:glass-medium transition-all duration-200 group"
      title={`Switch to ${isDarkTheme ? 'light' : 'dark'} mode`}
    >
      <div className="relative w-5 h-5">
        {isDarkTheme ? (
          // Moon icon for dark mode
          <svg
            className="w-5 h-5 transition-transform group-hover:scale-110"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"
              clipRule="evenodd"
            />
          </svg>
        ) : (
          // Sun icon for light mode
          <svg
            className="w-5 h-5 transition-transform group-hover:scale-110"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
              clipRule="evenodd"
            />
          </svg>
        )}
      </div>
    </button>
  );
}

// Glass/Normal Toggle Button
export function GlassNormalToggle() {
  const { toggleGlassNormal, isGlassTheme } = useTheme();

  return (
    <button
      onClick={toggleGlassNormal}
      className="glass-light p-2 rounded-lg hover:glass-medium transition-all duration-200 group"
      title={`Switch to ${isGlassTheme ? 'normal' : 'glass'} theme`}
    >
      <div className="relative w-5 h-5">
        {isGlassTheme ? (
          // Glass icon (sparkles/diamond)
          <svg
            className="w-5 h-5 transition-transform group-hover:scale-110"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 8.134a1 1 0 010 1.732L14.146 10.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 10.8 6.5 9.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z"
              clipRule="evenodd"
            />
          </svg>
        ) : (
          // Normal/solid icon (square)
          <svg
            className="w-5 h-5 transition-transform group-hover:scale-110"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path
              fillRule="evenodd"
              d="M3 4a1 1 0 011-1h12a1 1 0 011 1v12a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2v8h10V6H5z"
              clipRule="evenodd"
            />
          </svg>
        )}
      </div>
    </button>
  );
}

// Combined Toggle Controls
export function ThemeToggles() {
  return (
    <div className="flex items-center gap-2">
      <LightDarkToggle />
      <GlassNormalToggle />
    </div>
  );
}

// Enhanced Theme Selector Component (kept for advanced users)
export function ThemeSelector() {
  const { theme, setTheme, effectiveTheme, toggleTheme, themeConfig } = useTheme();

  const allThemes = Object.values(THEME_CONFIGS);
  const groupedThemes = {
    light: allThemes.filter(t => t.category === 'light'),
    dark: allThemes.filter(t => t.category === 'dark'),
    kaust: allThemes.filter(t => t.category === 'kaust'),
  };

  return (
    <div className="glass-card-sm">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">
            Current Theme
          </span>
          <button
            onClick={toggleTheme}
            className="glass-light px-2 py-1 text-xs rounded-md hover:glass-medium transition-all"
            title="Quick toggle between main themes"
          >
            🔄 Toggle
          </button>
        </div>

        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <span className="text-lg">{themeConfig.icon}</span>
            <div>
              <div className="text-sm font-medium">{themeConfig.displayName}</div>
              <div className="text-xs opacity-70">{themeConfig.description}</div>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          {Object.entries(groupedThemes).map(([category, themes]) => (
            <div key={category}>
              <div className="text-xs font-medium opacity-60 uppercase tracking-wide mb-1">
                {category}
              </div>
              <div className="grid grid-cols-1 gap-1">
                {themes.map((t) => (
                  <button
                    key={t.name}
                    onClick={() => setTheme(t.name as GlassTheme)}
                    className={`
                      flex items-center gap-2 px-2 py-1 text-xs rounded-md transition-all text-left
                      ${theme === t.name
                        ? 'glass-primary ring-1 ring-current'
                        : 'glass-light hover:glass-medium'
                      }
                    `}
                  >
                    <span>{t.icon}</span>
                    <span>{t.displayName}</span>
                    {theme === t.name && <span className="ml-auto">✓</span>}
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
