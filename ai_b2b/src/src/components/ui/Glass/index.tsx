'use client';

import React, { forwardRef } from 'react';
import { cn } from '@/lib/utils';

// Glass intensity types
export type GlassIntensity = 'light' | 'medium' | 'heavy' | 'ultra';
export type GlassColor = 'default' | 'primary' | 'secondary' | 'accent' | 'neutral' | 'blue' | 'navy';
export type GlassSize = 'sm' | 'md' | 'lg' | 'xl';

interface GlassProps extends React.HTMLAttributes<HTMLDivElement> {
  intensity?: GlassIntensity;
  color?: GlassColor;
  size?: GlassSize;
  interactive?: boolean;
  gradient?: boolean;
  children: React.ReactNode;
}

// Base Glass Component
export const Glass = forwardRef<HTMLDivElement, GlassProps>(
  ({ 
    className, 
    intensity = 'medium', 
    color = 'default', 
    size = 'md',
    interactive = false,
    gradient = false,
    children, 
    ...props 
  }, ref) => {
    const baseClasses = 'glass';
    const intensityClass = intensity !== 'medium' ? `glass-${intensity}` : '';
    const colorClass = color !== 'default' ? `glass-${color}` : '';
    const interactiveClass = interactive ? 'glass-interactive' : '';
    const gradientClass = gradient ? 'glass-bg-gradient-primary' : '';
    
    const sizeClasses = {
      sm: 'p-3 rounded-lg',
      md: 'p-4 rounded-xl',
      lg: 'p-6 rounded-2xl',
      xl: 'p-8 rounded-3xl',
    };

    return (
      <div
        ref={ref}
        className={cn(
          baseClasses,
          intensityClass,
          colorClass,
          interactiveClass,
          gradientClass,
          sizeClasses[size],
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Glass.displayName = 'Glass';

// Glass Card Component
interface GlassCardProps extends GlassProps {
  header?: React.ReactNode;
  footer?: React.ReactNode;
}

export const GlassCard = forwardRef<HTMLDivElement, GlassCardProps>(
  ({ header, footer, children, className, ...props }, ref) => {
    return (
      <Glass ref={ref} className={cn('flex flex-col', className)} {...props}>
        {header && (
          <div className="mb-4 pb-4 border-b border-white/20">
            {header}
          </div>
        )}
        <div className="flex-1">
          {children}
        </div>
        {footer && (
          <div className="mt-4 pt-4 border-t border-white/20">
            {footer}
          </div>
        )}
      </Glass>
    );
  }
);

GlassCard.displayName = 'GlassCard';

// Glass Button Component
interface GlassButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'primary' | 'secondary' | 'accent';
  size?: 'sm' | 'md' | 'lg';
  intensity?: GlassIntensity;
}

export const GlassButton = forwardRef<HTMLButtonElement, GlassButtonProps>(
  ({ 
    className, 
    variant = 'default', 
    size = 'md', 
    intensity = 'medium',
    children, 
    ...props 
  }, ref) => {
    const baseClasses = 'glass glass-interactive transition-all duration-200 font-medium focus-ring';
    const intensityClass = intensity !== 'medium' ? `glass-${intensity}` : '';
    
    const variantClasses = {
      default: '',
      primary: 'glass-primary',
      secondary: 'glass-secondary',
      accent: 'glass-accent',
    };

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm rounded-lg',
      md: 'px-4 py-2 text-sm rounded-xl',
      lg: 'px-6 py-3 text-base rounded-xl',
    };

    return (
      <button
        ref={ref}
        className={cn(
          baseClasses,
          intensityClass,
          variantClasses[variant],
          sizeClasses[size],
          'hover:scale-105 active:scale-95',
          className
        )}
        {...props}
      >
        {children}
      </button>
    );
  }
);

GlassButton.displayName = 'GlassButton';

// Glass Input Component
interface GlassInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  intensity?: GlassIntensity;
}

export const GlassInput = forwardRef<HTMLInputElement, GlassInputProps>(
  ({ className, intensity = 'light', ...props }, ref) => {
    const baseClasses = 'glass glass-light w-full px-4 py-2 text-sm bg-transparent border-0 focus-ring rounded-xl placeholder:opacity-60';
    const intensityClass = intensity !== 'light' ? `glass-${intensity}` : '';

    return (
      <input
        ref={ref}
        className={cn(baseClasses, intensityClass, className)}
        {...props}
      />
    );
  }
);

GlassInput.displayName = 'GlassInput';

// Glass Modal Component
interface GlassModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export const GlassModal: React.FC<GlassModalProps> = ({ 
  isOpen, 
  onClose, 
  children, 
  size = 'md' 
}) => {
  if (!isOpen) return null;

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <Glass 
        intensity="heavy" 
        size="lg"
        className={cn('relative w-full', sizeClasses[size])}
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 glass-light p-2 rounded-lg hover:glass-medium transition-all"
        >
          ✕
        </button>
        {children}
      </Glass>
    </div>
  );
};

// Glass Navigation Component
interface GlassNavProps extends React.HTMLAttributes<HTMLElement> {
  position?: 'top' | 'bottom' | 'left' | 'right';
}

export const GlassNav = forwardRef<HTMLElement, GlassNavProps>(
  ({ className, position = 'top', children, ...props }, ref) => {
    const positionClasses = {
      top: 'fixed top-0 left-0 right-0 z-40',
      bottom: 'fixed bottom-0 left-0 right-0 z-40',
      left: 'fixed top-0 left-0 bottom-0 z-40',
      right: 'fixed top-0 right-0 bottom-0 z-40',
    };

    return (
      <nav
        ref={ref}
        className={cn(
          'glass glass-heavy backdrop-blur-xl',
          positionClasses[position],
          className
        )}
        {...props}
      >
        {children}
      </nav>
    );
  }
);

GlassNav.displayName = 'GlassNav';

// Glass Badge Component
interface GlassBadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: GlassColor;
  size?: 'sm' | 'md' | 'lg';
}

export const GlassBadge = forwardRef<HTMLSpanElement, GlassBadgeProps>(
  ({ className, variant = 'primary', size = 'sm', children, ...props }, ref) => {
    const baseClasses = 'glass inline-flex items-center font-medium';
    const variantClass = `glass-${variant}`;
    
    const sizeClasses = {
      sm: 'px-2 py-1 text-xs rounded-md',
      md: 'px-3 py-1.5 text-sm rounded-lg',
      lg: 'px-4 py-2 text-base rounded-xl',
    };

    return (
      <span
        ref={ref}
        className={cn(
          baseClasses,
          variantClass,
          sizeClasses[size],
          className
        )}
        {...props}
      >
        {children}
      </span>
    );
  }
);

GlassBadge.displayName = 'GlassBadge';
