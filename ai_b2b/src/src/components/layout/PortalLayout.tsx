'use client';

import React from 'react';
import Header from './Header';
import Footer from './Footer';
import MainContent from './MainContent';
import { useTheme } from '@/components/theme/ThemeProvider';

interface PortalLayoutProps {
  children: React.ReactNode;
  className?: string;
  showHeader?: boolean;
  showFooter?: boolean;
}

export default function PortalLayout({
  children,
  className = '',
  showHeader = true,
  showFooter = true
}: PortalLayoutProps) {
  const { themeConfig } = useTheme();

  return (
    <div
      className={`min-h-screen flex flex-col ${className}`}
      style={{ background: themeConfig.colors.background }}
    >
      {showHeader && <Header />}
      <MainContent className="flex-1">
        {children}
      </MainContent>
      {showFooter && <Footer />}
    </div>
  );
}