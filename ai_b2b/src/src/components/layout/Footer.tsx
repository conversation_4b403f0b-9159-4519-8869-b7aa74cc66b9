import React from 'react';

interface FooterProps {
  className?: string;
}

export default function Footer({ className = '' }: FooterProps) {
  return (
    <footer className={`bg-gray-900 text-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center mb-4">
              <div className="h-8 w-32 bg-blue-600 rounded flex items-center justify-center">
                <span className="text-white font-bold text-sm">KAUST</span>
              </div>
            </div>
            <p className="text-gray-300 text-sm mb-4">
              King <PERSON> University of Science and Technology
            </p>
            <p className="text-gray-400 text-xs">
              Building a brighter future through science and technology
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-sm font-semibold text-gray-200 tracking-wider uppercase mb-4">
              Quick Links
            </h3>
            <ul className="space-y-2">
              <li>
                <div className="h-4 w-16 bg-gray-700 rounded animate-pulse"></div>
              </li>
              <li>
                <div className="h-4 w-20 bg-gray-700 rounded animate-pulse"></div>
              </li>
              <li>
                <div className="h-4 w-18 bg-gray-700 rounded animate-pulse"></div>
              </li>
              <li>
                <div className="h-4 w-14 bg-gray-700 rounded animate-pulse"></div>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-sm font-semibold text-gray-200 tracking-wider uppercase mb-4">
              Support
            </h3>
            <ul className="space-y-2">
              <li>
                <div className="h-4 w-12 bg-gray-700 rounded animate-pulse"></div>
              </li>
              <li>
                <div className="h-4 w-16 bg-gray-700 rounded animate-pulse"></div>
              </li>
              <li>
                <div className="h-4 w-14 bg-gray-700 rounded animate-pulse"></div>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-8 pt-8 border-t border-gray-800">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm">
              © 2024 King Abdullah University of Science and Technology. All rights reserved.
            </div>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <div className="h-4 w-16 bg-gray-700 rounded animate-pulse"></div>
              <div className="h-4 w-20 bg-gray-700 rounded animate-pulse"></div>
              <div className="h-4 w-18 bg-gray-700 rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}