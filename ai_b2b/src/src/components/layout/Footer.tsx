'use client';

import React from 'react';
import Link from 'next/link';
import { Glass } from '@/components/ui/Glass';

interface FooterProps {
  className?: string;
}

export default function Footer({ className = '' }: FooterProps) {
  return (
    <footer className={`mt-auto ${className}`}>
      <Glass intensity="heavy" className="border-t border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center gap-3 mb-4">
                <Glass intensity="heavy" color="primary" className="p-2 rounded-full">
                  <div className="w-6 h-6 flex items-center justify-center">
                    <div className="grid grid-cols-2 gap-0.5">
                      <div className="w-2 h-2 bg-kaust-gold rounded-full"></div>
                      <div className="w-2 h-2 bg-kaust-orange rounded-full"></div>
                      <div className="w-2 h-2 bg-kaust-teal rounded-full"></div>
                      <div className="w-2 h-2 bg-kaust-grey rounded-full"></div>
                    </div>
                  </div>
                </Glass>
                <div>
                  <h3 className="text-lg font-bold kaust-text-gradient">KAUST B2B</h3>
                  <p className="text-xs opacity-70">Marketplace</p>
                </div>
              </div>
              <p className="opacity-80 text-sm mb-4">
                King Abdullah University of Science and Technology
              </p>
              <p className="opacity-60 text-xs">
                Building a brighter future through science and technology
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-sm font-semibold opacity-90 tracking-wider uppercase mb-4">
                Quick Links
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/" className="text-sm opacity-70 hover:opacity-100 transition-opacity">
                    Home
                  </Link>
                </li>
                <li>
                  <Link href="/products" className="text-sm opacity-70 hover:opacity-100 transition-opacity">
                    Products
                  </Link>
                </li>
                <li>
                  <Link href="/suppliers" className="text-sm opacity-70 hover:opacity-100 transition-opacity">
                    Suppliers
                  </Link>
                </li>
                <li>
                  <Link href="/theme-showcase" className="text-sm opacity-70 hover:opacity-100 transition-opacity">
                    Theme Showcase
                  </Link>
                </li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h3 className="text-sm font-semibold opacity-90 tracking-wider uppercase mb-4">
                Support
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/help" className="text-sm opacity-70 hover:opacity-100 transition-opacity">
                    Help Center
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-sm opacity-70 hover:opacity-100 transition-opacity">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="/docs" className="text-sm opacity-70 hover:opacity-100 transition-opacity">
                    Documentation
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="mt-8 pt-8 border-t border-white/20">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="opacity-60 text-sm">
                © 2024 King Abdullah University of Science and Technology. All rights reserved.
              </div>
              <div className="flex space-x-6 mt-4 md:mt-0">
                <Link href="/privacy" className="text-sm opacity-60 hover:opacity-100 transition-opacity">
                  Privacy Policy
                </Link>
                <Link href="/terms" className="text-sm opacity-60 hover:opacity-100 transition-opacity">
                  Terms of Service
                </Link>
                <Link href="/accessibility" className="text-sm opacity-60 hover:opacity-100 transition-opacity">
                  Accessibility
                </Link>
              </div>
            </div>
          </div>
        </div>
      </Glass>
    </footer>
  );
}