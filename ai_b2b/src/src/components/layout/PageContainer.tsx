'use client';

import React from 'react';
import { Glass } from '@/components/ui/Glass';

interface PageContainerProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '7xl' | 'full';
}

const maxWidthClasses = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  '7xl': 'max-w-7xl',
  full: 'max-w-full'
};

export default function PageContainer({
  children,
  title,
  subtitle,
  className = '',
  maxWidth = '7xl'
}: PageContainerProps) {
  return (
    <div className={`${maxWidthClasses[maxWidth]} mx-auto px-4 sm:px-6 lg:px-8 ${className}`}>
      {(title || subtitle) && (
        <Glass intensity="light" className="mb-8 p-6">
          {title && (
            <h1 className="text-3xl font-bold mb-2 kaust-text-gradient">
              {title}
            </h1>
          )}
          {subtitle && (
            <p className="text-lg opacity-80">
              {subtitle}
            </p>
          )}
        </Glass>
      )}
      {children}
    </div>
  );
}