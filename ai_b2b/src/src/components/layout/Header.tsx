'use client';

import React from 'react';
import Link from 'next/link';
import { Glass, GlassButton } from '@/components/ui/Glass';
import { ThemeSelector } from '@/components/theme/ThemeProvider';

interface HeaderProps {
  className?: string;
}

export default function Header({ className = '' }: HeaderProps) {
  return (
    <header className={`sticky top-0 z-50 ${className}`}>
      <Glass intensity="heavy" className="backdrop-blur-xl border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* KAUST Logo */}
            <Link href="/" className="flex items-center">
              <div className="flex items-center gap-3">
                <Glass intensity="heavy" color="primary" className="p-2 rounded-full">
                  <div className="w-6 h-6 flex items-center justify-center">
                    <div className="grid grid-cols-2 gap-0.5">
                      <div className="w-2 h-2 bg-kaust-gold rounded-full"></div>
                      <div className="w-2 h-2 bg-kaust-orange rounded-full"></div>
                      <div className="w-2 h-2 bg-kaust-teal rounded-full"></div>
                      <div className="w-2 h-2 bg-kaust-grey rounded-full"></div>
                    </div>
                  </div>
                </Glass>
                <div>
                  <h1 className="text-lg font-bold kaust-text-gradient">KAUST B2B</h1>
                  <p className="text-xs opacity-70">Marketplace</p>
                </div>
              </div>
            </Link>

            {/* Navigation */}
            <nav className="hidden md:flex space-x-6">
              <Link href="/" className="text-sm font-medium hover:opacity-80 transition-opacity">
                Home
              </Link>
              <Link href="/products" className="text-sm font-medium hover:opacity-80 transition-opacity">
                Products
              </Link>
              <Link href="/suppliers" className="text-sm font-medium hover:opacity-80 transition-opacity">
                Suppliers
              </Link>
              <Link href="/orders" className="text-sm font-medium hover:opacity-80 transition-opacity">
                Orders
              </Link>
            </nav>

            {/* Search */}
            <div className="hidden lg:flex items-center">
              <Glass intensity="light" className="px-4 py-2 w-64">
                <input
                  type="text"
                  placeholder="Search products..."
                  className="w-full bg-transparent border-0 outline-none text-sm placeholder:opacity-60"
                />
              </Glass>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-3">
              <Link href="/theme-showcase">
                <GlassButton variant="primary" size="sm">
                  🎨 Themes
                </GlassButton>
              </Link>

              <ThemeSelector />

              <GlassButton variant="secondary" size="sm">
                Sign In
              </GlassButton>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <GlassButton variant="default" size="sm">
                <svg
                  className="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </GlassButton>
            </div>
          </div>
        </div>
      </Glass>
    </header>
  );
}