import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Theme utilities
export function getThemeColors(theme: string) {
  const themeMap = {
    'glass-light': {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#EF8721',
      background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
    },
    'glass-dark': {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#EF8721',
      background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
    },
    'glass-kaust': {
      primary: '#F9C217',
      secondary: '#1F9EA8',
      accent: '#EF8721',
      background: 'linear-gradient(135deg, #1F9EA8 0%, #0077b6 50%, #023e8a 100%)',
    },
    'glass-kaust-warm': {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#0077b6',
      background: 'linear-gradient(135deg, #F9C217 0%, #EF8721 100%)',
    },
    'glass-kaust-cool': {
      primary: '#1F9EA8',
      secondary: '#F9C217',
      accent: '#F9C217',
      background: 'linear-gradient(135deg, #0077b6 0%, #023e8a 100%)',
    },
  };
  
  return themeMap[theme as keyof typeof themeMap] || themeMap['glass-light'];
}

// Glass effect utilities
export function createGlassStyle(
  intensity: 'light' | 'medium' | 'heavy' | 'ultra' = 'medium',
  color?: string
) {
  const intensityMap = {
    light: {
      opacity: 0.08,
      blur: 8,
      shadow: '0 4px 16px rgba(0, 0, 0, 0.1)',
    },
    medium: {
      opacity: 0.12,
      blur: 16,
      shadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
    },
    heavy: {
      opacity: 0.18,
      blur: 24,
      shadow: '0 12px 48px rgba(0, 0, 0, 0.15)',
    },
    ultra: {
      opacity: 0.25,
      blur: 32,
      shadow: '0 16px 64px rgba(0, 0, 0, 0.2)',
    },
  };

  const config = intensityMap[intensity];
  const baseColor = color || '255, 255, 255';

  return {
    background: `rgba(${baseColor}, ${config.opacity})`,
    backdropFilter: `blur(${config.blur}px)`,
    WebkitBackdropFilter: `blur(${config.blur}px)`,
    boxShadow: config.shadow,
    border: `1px solid rgba(${baseColor}, 0.2)`,
  };
}

// Animation utilities for glass components
export const glassAnimations = {
  fadeIn: 'animate-fade-in',
  slideUp: 'animate-slide-up',
  scaleIn: 'animate-scale-in',
  float: 'animate-float',
};

// Responsive glass utilities
export function getResponsiveGlass(
  mobile: string = 'glass-light',
  tablet: string = 'glass-medium',
  desktop: string = 'glass-heavy'
) {
  return `${mobile} md:${tablet} lg:${desktop}`;
}

// Color conversion utilities
export function hexToRgba(hex: string, alpha: number = 1): string {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

export function rgbaToHex(rgba: string): string {
  const match = rgba.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/);
  if (!match) return '#000000';
  
  const [, r, g, b] = match;
  return `#${[r, g, b].map(x => parseInt(x).toString(16).padStart(2, '0')).join('')}`;
}

// Theme detection utilities
export function detectSystemTheme(): 'light' | 'dark' {
  if (typeof window === 'undefined') return 'light';
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
}

export function detectReducedMotion(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

// Glass component validation
export function validateGlassProps(props: Record<string, any>) {
  const validIntensities = ['light', 'medium', 'heavy', 'ultra'];
  const validColors = ['default', 'primary', 'secondary', 'accent', 'neutral', 'blue', 'navy'];
  const validSizes = ['sm', 'md', 'lg', 'xl'];

  const errors: string[] = [];

  if (props.intensity && !validIntensities.includes(props.intensity)) {
    errors.push(`Invalid intensity: ${props.intensity}. Must be one of: ${validIntensities.join(', ')}`);
  }

  if (props.color && !validColors.includes(props.color)) {
    errors.push(`Invalid color: ${props.color}. Must be one of: ${validColors.join(', ')}`);
  }

  if (props.size && !validSizes.includes(props.size)) {
    errors.push(`Invalid size: ${props.size}. Must be one of: ${validSizes.join(', ')}`);
  }

  return errors;
}
