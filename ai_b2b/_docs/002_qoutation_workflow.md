reference credible B2B platforms like Adobe Commerce, commercetools, Magento, and more. Here’s a detailed review and enhancement suggestions to refine it into a production-ready **quotation/order state machine** for your B2B e-commerce system:

---

## ✅ Strengths of Your Workflow

✔️ **Realistic and complete**: Includes negotiation loops (`Updated → UnderReview`), expiry, and terminal states
✔️ **Roles clearly mapped**: Buyer and seller actions are well defined
✔️ **Terminology aligned** with B2B systems like Odoo, Magento, Adobe Commerce
✔️ **Implementation-ready**: Includes auditability, versioning, notifications, and PDF generation

---

## 🛠️ Suggestions to Improve

### 1. **State Naming Consistency**

Use active verb-like names to match business logic/state engines (especially in Odoo or workflow engines).

| Current State   | Suggested Alternative          |
| --------------- | ------------------------------ |
| `Draft`       | ✅ (keep as is)                |
| `Submitted`   | `SubmittedForReview`         |
| `UnderReview` | ✅ (keep as is)                |
| `Updated`     | `Resubmitted` or `Revised` |
| `Accepted`    | `Approved`                   |
| `Ordered`     | `ConvertedToOrder`           |

> Or define aliases for UI display vs. internal logic (e.g., `state="approved"` and label="Accepted").

---

### 2. **Add Optional States (Advanced Cases)**

If your B2B customers like **KAUST** require procurement steps or approvals:

| State                        | When Needed                                     |
| ---------------------------- | ----------------------------------------------- |
| `AwaitingCustomerApproval` | When a quotation needs department head approval |
| `OnHold`                   | When paused for internal clarification          |
| `Withdrawn`                | Buyer cancels before review begins              |

---

### 3. **Enhance Mermaid Diagram**

You can make the diagram more expressive by showing optional loops and decision paths like this:

```mermaid
stateDiagram-v2
    [*] --> Draft
    Draft --> SubmittedForReview : Buyer/Seller submits
    SubmittedForReview --> UnderReview : Auto-transition
    UnderReview --> Revised : Counterparty requests changes
    Revised --> UnderReview : Resubmitted after edit
    UnderReview --> Approved : Terms accepted
    UnderReview --> Rejected : Declined
    Approved --> ConvertedToOrder : PO created
    Approved --> Expired : Not converted in time
    Rejected --> [*]
    Expired --> [*]
    ConvertedToOrder --> [*]
```

---

## 📌 Final Quotation State List (Optimized)

| State ID                             | Label                      | Description                        |
| ------------------------------------ | -------------------------- | ---------------------------------- |
| `draft`                            | Draft                      | Buyer editing quotation/cart       |
| `submitted`                        | Submitted                  | Sent to seller/customer for review |
| `under_review`                     | Under Review               | Counterparty evaluating            |
| `revised`                          | Revised                    | Updated due to negotiation         |
| `approved`                         | Accepted                   | Agreement reached                  |
| `ordered`                          | Ordered                    | Turned into official order         |
| `expired`                          | Expired                    | Validity period lapsed             |
| `rejected`                         | Rejected                   | Declined by either side            |
| `on_hold` *(optional)*           | On Hold                    | Temporarily paused                 |
| `awaiting_approval` *(optional)* | Awaiting Internal Approval | Requires further clearance         |

---

## Workflow scenarios in a B2B e-commerce platform

where **quotation = cart/order**

---

## ✅ Scenario 1: Basic Quote Submission and Order Conversion

### 🔹 Description:

Customer builds a cart, submits a quotation to the vendor, vendor accepts it, and the quote is converted to a purchase order.

---

### 🧩 Sequence Diagram:

```mermaid
sequenceDiagram
    participant Customer
    participant Portal
    participant Vendor
    participant Backend

    Customer->>Portal: Create Draft Quotation (Cart)
    Portal->>Backend: Save Draft
    Customer->>Portal: Submit Quotation
    Portal->>Backend: Change State to SubmittedForReview
    Backend->>Vendor: Notify for Review

    Vendor->>Backend: Accept Quotation
    Backend->>Portal: State → Approved
    Customer->>Portal: Convert to Order
    Portal->>Backend: State → ConvertedToOrder
```

---

### ✅ Explanation:

* Quotation moves from `draft` → `submitted_for_review` → `approved` → `ordered`.
* This is a **straightforward no-negotiation case**, common for repeat orders with agreed pricing.

---

## ✅ Scenario 2: Quotation with Negotiation (Vendor Revises Terms)

### 🔹 Description:

Customer submits a quote, vendor modifies pricing, customer accepts the update, then it’s converted to an order.

---

### 🧩 Sequence Diagram:

```mermaid
sequenceDiagram
    participant Customer
    participant Portal
    participant Vendor
    participant Backend

    Customer->>Portal: Create Draft Quote
    Portal->>Backend: Save Draft
    Customer->>Portal: Submit Quote
    Portal->>Backend: State → SubmittedForReview
    Backend->>Vendor: Notify for Review

    Vendor->>Backend: Revise Quote (Change Discount/Terms)
    Backend->>Portal: State → Revised
    Customer->>Portal: Review Changes
    Customer->>Portal: Accept Terms
    Portal->>Backend: State → Approved
    Customer->>Portal: Convert to Order
    Backend->>Portal: State → ConvertedToOrder
```

---

### ✅ Explanation:

* Shows negotiation loop with state `revised` (or `updated`).
* System allows **version tracking** and auditability.
* Clear negotiation lifecycle: `submitted → under_review → revised → approved → converted`.

---

## ✅ Scenario 3: Customer Submits but Quotation Is Rejected

### 🔹 Description:

Customer creates a quote and submits it, but the vendor rejects it (e.g., out-of-stock or terms mismatch).

---

### 🧩 Sequence Diagram:

```mermaid
sequenceDiagram
    participant Customer
    participant Portal
    participant Vendor
    participant Backend

    Customer->>Portal: Create Quote
    Portal->>Backend: Save as Draft
    Customer->>Portal: Submit Quote
    Backend->>Vendor: Notify for Review
    Vendor->>Backend: Reject Quote
    Backend->>Portal: State → Rejected
    Portal->>Customer: Notify Rejection
```

---

### ✅ Explanation:

* Ends in `rejected` state.
* Customer may clone/duplicate the quote later to retry.
* Important for **reporting and feedback loops** to include rejection reasons.

---

## ✅ Scenario 4: Quote Approved but Not Ordered (Expires)

### 🔹 Description:

Quotation is accepted, but the customer doesn’t convert it to an order in time — it expires.

---

### 🧩 Sequence Diagram:

```mermaid
sequenceDiagram
    participant Customer
    participant Portal
    participant Vendor
    participant Backend
    participant Scheduler

    Customer->>Portal: Submit Quote
    Vendor->>Portal: Accept Quote
    Portal->>Backend: State → Approved
    Note right of Scheduler: After 30 days (validity rule)
    Scheduler->>Backend: Check Quote Validity
    Backend->>Portal: State → Expired
    Portal->>Customer: Notify Expiry
```

---

### ✅ Explanation:

* Introduces time-based logic using a scheduler (e.g., cron job).
* Important for compliance and quote lifecycle management.

---

## ✅ Scenario 5: Internal Customer Approval Required

### 🔹 Description:

Customer creates quote, but internal manager must approve before it’s submitted to the vendor.

---

### 🧩 Sequence Diagram:

```mermaid
sequenceDiagram
    participant CustomerRep
    participant Portal
    participant Manager
    participant Backend
    participant Vendor

    CustomerRep->>Portal: Create Draft Quote
    Portal->>Backend: Save Draft
    CustomerRep->>Manager: Request Internal Approval
    Manager->>Portal: Approve Quote
    Portal->>Backend: State → SubmittedForReview
    Backend->>Vendor: Notify for Review
```

---

### ✅ Explanation:

* Adds internal approval before formal submission.
* `awaiting_internal_approval` could be added as a **pre-submission state** if needed.

---

## 📌 Summary of All States Covered

| State                 | Trigger                                 |
| --------------------- | --------------------------------------- |
| `draft`             | Created by customer (cart)              |
| `submitted`         | Sent to vendor `submitted_for_review` |
| `under_review`      | Vendor reviewing terms                  |
| `revised`           | Vendor modifies and sends back          |
| `approved`          | Agreed by both parties                  |
| `ordered`           | Finalized by customer                   |
| `rejected`          | Vendor declines                         |
| `expired`           | System auto-expires (validity\_date)    |
| `awaiting_approval` | Optional internal approval state        |

---

Would you like this exported into a visual file (SVG, PNG, PDF), or should I generate an **activity diagram** next to show transitions at a high level?

## references

[1] https://www.cflowapps.co.uk/quotation-process-automation-2/
[2] https://bsscommerce.com/shopify/quotation-page-b2b-ecommerce/
[3] https://experienceleague.adobe.com/en/docs/commerce-admin/b2b/quotes/quotes
[4] https://www.oscprofessionals.com/blog/enquiry-quote-and-negotiation-cycle-for-b2b/
[5] https://commercetools.com/blog/b2b-product-spotlight-automated-quote-management
[6] https://experienceleague.adobe.com/en/docs/commerce-admin/b2b/quotes/quote-price-negotiation
[7] https://aheadworks.com/cart-to-quote-extension-for-magento-2
[8] https://www.theprovatogroup.com/applications/how-quote-carts-work/
[9] https://virtocommerce.com/blog/b2b-ecommerce-quote-management
[10] https://www.sendcloud.com/negotiating-carrier-contracts-ecommerce/
[11] https://www.clarity-ventures.com/ecommerce/b2b-quotation-management
[12] https://wpexperts.io/blog/quote-management-in-b2b-ecommerce/
[13] https://oroinc.com/b2b-ecommerce/blog/workflow-automation-in-ecommerce/
[14] https://www.linkedin.com/advice/1/how-can-you-negotiate-establish-successful-1yd7e
[15] https://theretailexec.com/logistics/order-management-workflow/
[16] https://www.linkedin.com/advice/3/how-do-you-manage-expectations-when-negotiating-terms-7pwmc
[17] https://firebearstudio.com/blog/magento-2-b2b-negotiation-quotes-import-export.html
[18] https://www.youtube.com/watch?v=ER6RSF-QLLc
