# KAUST Glassmorphism Theme System

## Overview

The KAUST Glassmorphism Theme System is a comprehensive design system that provides dynamic theming capabilities with beautiful glass effects, KAUST branding integration, and easy theme switching functionality.

## Features

### 🎨 Dynamic Theme Switching

- **5 Built-in Themes**: Light, Dark, KAUST, KAUST Warm, KAUST Cool
- **Real-time Switching**: Instant theme changes without page reload
- **Persistent Storage**: Theme preferences saved in localStorage
- **Auto-detection**: Automatic system theme detection support

### 🏛️ KAUST Branding Integration

- **Official Colors**: All KAUST brand colors integrated
- **Multiple Variants**: Different KAUST-themed options
- **Brand Consistency**: Maintains KAUST visual identity
- **Logo Integration**: KAUST logo color scheme representation

### ✨ Advanced Glassmorphism Effects

- **4 Intensity Levels**: Light, Medium, Heavy, Ultra
- **6 Color Variants**: Primary, Secondary, Accent, Neutral, Blue, Navy
- **Dynamic Opacity**: Adaptive transparency based on theme
- **Backdrop Blur**: Hardware-accelerated blur effects
- **Layered Shadows**: Multi-level shadow system

## Theme Configuration

### Available Themes

1. **Glass Light** (`glass-light`)

   - Clean and bright glassmorphism
   - Subtle transparency effects
   - Light background gradients
2. **Glass Dark** (`glass-dark`)

   - Sophisticated dark glassmorphism
   - Enhanced depth and contrast
   - Dark background gradients
3. **Glass KAUST** (`glass-kaust`)

   - Official KAUST branding
   - Teal and gold color scheme
   - KAUST gradient backgrounds
4. **Glass KAUST Warm** (`glass-kaust-warm`)

   - Warm KAUST variant
   - Gold and orange prominence
   - Warm gradient backgrounds
5. **Glass KAUST Cool** (`glass-kaust-cool`)

   - Cool KAUST variant
   - Blue and navy prominence
   - Cool gradient backgrounds

## Usage

### Basic Setup

```tsx
import { ThemeProvider } from '@/components/theme/ThemeProvider';

function App() {
  return (
    <ThemeProvider defaultTheme="glass-kaust" enableAutoDetection={true}>
      {/* Your app content */}
    </ThemeProvider>
  );
}
```

### Theme Switching

```tsx
import { useTheme, ThemeSelector } from '@/components/theme/ThemeProvider';

function MyComponent() {
  const { theme, setTheme, toggleTheme, themeConfig } = useTheme();
  
  return (
    <div>
      <ThemeSelector /> {/* Built-in theme selector */}
      <button onClick={toggleTheme}>Quick Toggle</button>
      <button onClick={() => setTheme('glass-kaust')}>KAUST Theme</button>
    </div>
  );
}
```

### Glass Components

```tsx
import { Glass, GlassCard, GlassButton } from '@/components/ui/Glass';

function Example() {
  return (
    <div>
      {/* Basic glass container */}
      <Glass intensity="medium" color="primary">
        Content here
      </Glass>
    
      {/* Glass card with header/footer */}
      <GlassCard 
        header={<h3>Card Title</h3>}
        footer={<button>Action</button>}
        intensity="heavy"
        color="secondary"
      >
        Card content
      </GlassCard>
    
      {/* Glass button */}
      <GlassButton variant="primary" size="lg">
        Click me
      </GlassButton>
    </div>
  );
}
```

## Component API

### Glass Component Props

```tsx
interface GlassProps {
  intensity?: 'light' | 'medium' | 'heavy' | 'ultra';
  color?: 'default' | 'primary' | 'secondary' | 'accent' | 'neutral' | 'blue' | 'navy';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  interactive?: boolean;
  gradient?: boolean;
  children: React.ReactNode;
}
```

### Theme Provider Props

```tsx
interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: GlassTheme;
  enableAutoDetection?: boolean;
  customerId?: string;
}
```

## CSS Variables

### Glass Effect Variables

```css
:root {
  /* Opacity levels */
  --glass-opacity-light: 0.08;
  --glass-opacity-medium: 0.12;
  --glass-opacity-heavy: 0.18;
  --glass-opacity-ultra: 0.25;
  
  /* Blur levels */
  --glass-blur-light: 8px;
  --glass-blur-medium: 16px;
  --glass-blur-heavy: 24px;
  --glass-blur-ultra: 32px;
  
  /* Border and shadow */
  --glass-border-opacity: 0.2;
  --glass-shadow-opacity: 0.1;
  --glass-highlight-opacity: 0.3;
}
```

### KAUST Brand Colors

```css
:root {
  --kaust-gold: #F9C217;
  --kaust-orange: #EF8721;
  --kaust-teal: #1F9EA8;
  --kaust-grey: #958B83;
  --kaust-blue: #0077b6;
  --kaust-navy: #023e8a;
}
```

## Utility Classes

### Glass Base Classes

- `.glass` - Base glass effect
- `.glass-light` - Light intensity
- `.glass-medium` - Medium intensity
- `.glass-heavy` - Heavy intensity
- `.glass-ultra` - Ultra intensity

### Glass Color Classes

- `.glass-primary` - KAUST teal glass
- `.glass-secondary` - KAUST gold glass
- `.glass-accent` - KAUST orange glass
- `.glass-neutral` - KAUST grey glass
- `.glass-blue` - KAUST blue glass
- `.glass-navy` - KAUST navy glass

### Interactive Classes

- `.glass-interactive` - Hover and focus effects
- `.glass-card` - Card-style glass container
- `.glass-card-sm` - Small card variant
- `.glass-card-lg` - Large card variant

## Performance Considerations

### Hardware Acceleration

- All blur effects use `backdrop-filter` for hardware acceleration
- Fallback support with `-webkit-backdrop-filter`
- Optimized for modern browsers

### Reduced Motion Support

- Respects `prefers-reduced-motion` setting
- Disables animations for accessibility
- Maintains visual hierarchy without motion

### Mobile Optimization

- Reduced blur intensity on smaller screens
- Optimized glass effects for touch devices
- Responsive design patterns

## Browser Support

- **Modern Browsers**: Full support with hardware acceleration
- **Safari**: Full support with webkit prefixes
- **Firefox**: Full support (backdrop-filter since v103)
- **Chrome/Edge**: Full support
- **Fallback**: Graceful degradation to solid backgrounds

## Customization

### Creating Custom Themes

```tsx
const customTheme: ThemeConfig = {
  name: 'custom-theme',
  displayName: 'Custom Theme',
  description: 'My custom glassmorphism theme',
  icon: '🎨',
  category: 'custom',
  colors: {
    primary: '#your-color',
    secondary: '#your-color',
    accent: '#your-color',
    background: 'linear-gradient(...)',
  },
};
```

### Extending Glass Effects

```css
.custom-glass {
  background: rgba(your-color, var(--glass-opacity-medium));
  backdrop-filter: blur(var(--glass-blur-medium));
  border: 1px solid rgba(your-color, var(--glass-border-opacity));
  box-shadow: 0 8px 32px rgba(your-color, 0.15);
}
```

## Examples

Visit `/theme-showcase` to see all themes and components in action with:

- Live theme switching
- Component demonstrations
- Color palette displays
- Interactive examples
- Layout patterns

## Best Practices

1. **Use appropriate intensity levels** for content hierarchy
2. **Maintain sufficient contrast** for accessibility
3. **Test on different devices** for performance
4. **Consider reduced motion** preferences
5. **Use KAUST themes** for brand consistency
6. **Layer glass effects** thoughtfully for depth
7. **Optimize for mobile** with lighter effects

## Troubleshooting

### Common Issues

1. **Blur not working**: Check browser support for `backdrop-filter`
2. **Performance issues**: Reduce blur intensity or use fewer glass elements
3. **Theme not persisting**: Ensure localStorage is available
4. **Colors not updating**: Check CSS variable inheritance

### Debug Mode

Enable debug mode to see theme variables:

```tsx
<ThemeProvider defaultTheme="glass-kaust" debug={true}>
  {/* App content */}
</ThemeProvider>
```