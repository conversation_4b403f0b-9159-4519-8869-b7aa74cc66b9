# F009 - Order States Workflow

## Feature Overview

**Priority**: P0 - Critical (Foundation)  
**Phase**: 4 - Order Management Core  
**Estimated Effort**: 5-7 days  
**Dependencies**: F008 - Basic Order Management

## Business Context

The Order States Workflow implements the comprehensive B2B procurement lifecycle for the KAUST B2B Marketplace. This feature establishes the state machine that governs order transitions from initial draft through final completion, enabling proper B2B workflow management with approval processes and vendor interactions.

## Technical Objectives

1. **State Machine Implementation**: Create robust state machine for order lifecycle management
2. **Transition Logic**: Implement business rules for state transitions and validations
3. **Workflow Automation**: Automate state transitions where appropriate
4. **Audit Trail**: Track all state changes and transitions for compliance
5. **User Interface**: Provide clear visual indicators and actions for each state

## Functional Requirements

### Order State Machine

#### Core States Definition
```typescript
enum OrderState {
  DRAFT = 'draft',                    // User building order
  SAVED = 'saved',                    // Order saved for later
  SUBMITTED = 'submitted',            // Submitted for vendor review
  UNDER_REVIEW = 'under_review',      // Vendor reviewing order
  REVISED = 'revised',                // Vendor requested changes
  APPROVED = 'approved',              // Vendor approved order
  REJECTED = 'rejected',              // Vendor rejected order
  EXPIRED = 'expired',                // Order expired without action
  CANCELLED = 'cancelled',            // User cancelled order
  CONVERTED_TO_ORDER = 'order'        // Converted to final order
}
```

#### State Transition Rules
```typescript
interface StateTransition {
  from: OrderState;
  to: OrderState;
  trigger: TransitionTrigger;
  conditions?: TransitionCondition[];
  actions?: TransitionAction[];
  permissions?: string[];
  validation?: ValidationRule[];
}

type TransitionTrigger = 
  | 'user_submit'
  | 'vendor_review'
  | 'vendor_approve'
  | 'vendor_reject'
  | 'vendor_request_revision'
  | 'user_revise'
  | 'user_cancel'
  | 'system_expire'
  | 'user_convert';
```

#### Workflow State Machine
```typescript
const orderWorkflow: StateTransition[] = [
  // Draft to Submitted
  {
    from: OrderState.DRAFT,
    to: OrderState.SUBMITTED,
    trigger: 'user_submit',
    conditions: ['has_items', 'valid_quantities', 'complete_information'],
    actions: ['notify_vendor', 'lock_editing', 'set_submission_date'],
    permissions: ['submit_order']
  },
  
  // Submitted to Under Review
  {
    from: OrderState.SUBMITTED,
    to: OrderState.UNDER_REVIEW,
    trigger: 'vendor_review',
    actions: ['start_review_timer', 'notify_customer'],
    permissions: ['vendor_review']
  },
  
  // Under Review to Approved
  {
    from: OrderState.UNDER_REVIEW,
    to: OrderState.APPROVED,
    trigger: 'vendor_approve',
    actions: ['set_approval_date', 'notify_customer', 'set_expiry_date'],
    permissions: ['vendor_approve']
  },
  
  // Under Review to Revised
  {
    from: OrderState.UNDER_REVIEW,
    to: OrderState.REVISED,
    trigger: 'vendor_request_revision',
    actions: ['unlock_editing', 'notify_customer', 'add_revision_notes'],
    permissions: ['vendor_revise']
  },
  
  // Revised back to Under Review
  {
    from: OrderState.REVISED,
    to: OrderState.UNDER_REVIEW,
    trigger: 'user_revise',
    conditions: ['has_changes', 'valid_revision'],
    actions: ['lock_editing', 'notify_vendor', 'increment_version'],
    permissions: ['revise_order']
  },
  
  // Approved to Order
  {
    from: OrderState.APPROVED,
    to: OrderState.CONVERTED_TO_ORDER,
    trigger: 'user_convert',
    conditions: ['within_validity_period', 'approved_by_vendor'],
    actions: ['create_purchase_order', 'notify_all_parties', 'archive_quotation'],
    permissions: ['convert_order']
  }
];
```

### State Management Implementation

#### Workflow Engine
```typescript
class OrderWorkflowEngine {
  async transitionOrder(
    orderId: string, 
    trigger: TransitionTrigger, 
    context?: TransitionContext
  ): Promise<OrderStateTransitionResult> {
    const order = await this.getOrder(orderId);
    const transition = this.findValidTransition(order.state, trigger);
    
    if (!transition) {
      throw new InvalidTransitionError(`Cannot ${trigger} from ${order.state}`);
    }
    
    // Validate conditions
    await this.validateConditions(order, transition.conditions, context);
    
    // Check permissions
    await this.checkPermissions(transition.permissions, context.user);
    
    // Execute transition
    const result = await this.executeTransition(order, transition, context);
    
    // Execute actions
    await this.executeActions(order, transition.actions, context);
    
    return result;
  }
  
  private async validateConditions(
    order: Order, 
    conditions: TransitionCondition[], 
    context: TransitionContext
  ): Promise<void> {
    for (const condition of conditions) {
      const isValid = await this.evaluateCondition(order, condition, context);
      if (!isValid) {
        throw new ConditionNotMetError(`Condition ${condition} not met`);
      }
    }
  }
  
  private async executeActions(
    order: Order, 
    actions: TransitionAction[], 
    context: TransitionContext
  ): Promise<void> {
    for (const action of actions) {
      await this.executeAction(order, action, context);
    }
  }
}
```

#### State Validation
```typescript
interface StateValidator {
  validateDraftOrder(order: Order): ValidationResult;
  validateSubmissionReadiness(order: Order): ValidationResult;
  validateRevisionChanges(order: Order, previousVersion: Order): ValidationResult;
  validateConversionEligibility(order: Order): ValidationResult;
}

interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}
```

### User Interface Integration

#### State Display Components
```typescript
interface OrderStateIndicator {
  state: OrderState;
  showProgress?: boolean;
  showActions?: boolean;
  compact?: boolean;
}

interface OrderActionButton {
  action: TransitionTrigger;
  label: string;
  variant: 'primary' | 'secondary' | 'danger';
  confirmationRequired?: boolean;
  permissions?: string[];
}
```

#### State-Specific UI Elements
- **Draft State**: Edit buttons, save actions, submit button
- **Submitted State**: Status indicator, cancel option (if allowed)
- **Under Review**: Progress indicator, estimated completion time
- **Revised**: Revision notes, change highlights, resubmit button
- **Approved**: Convert to order button, expiry countdown
- **Rejected**: Rejection reason, create new order option
- **Expired**: Expiry notice, renewal options
- **Cancelled**: Cancellation reason, restore option (if allowed)

### Audit Trail and History

#### State Change Tracking
```typescript
interface OrderStateChange {
  id: string;
  orderId: string;
  fromState: OrderState;
  toState: OrderState;
  trigger: TransitionTrigger;
  triggeredBy: string;
  triggeredAt: Date;
  context?: Record<string, any>;
  notes?: string;
  version: number;
}

interface OrderHistory {
  orderId: string;
  changes: OrderStateChange[];
  currentState: OrderState;
  createdAt: Date;
  lastModified: Date;
}
```

#### Audit Features
- **Complete History**: Track all state changes with timestamps
- **User Attribution**: Record who triggered each transition
- **Context Capture**: Store relevant context for each transition
- **Version Control**: Track order versions through revisions
- **Compliance Reporting**: Generate audit reports for compliance

### Notification Integration

#### State Change Notifications
```typescript
interface StateChangeNotification {
  orderId: string;
  orderName: string;
  fromState: OrderState;
  toState: OrderState;
  recipients: NotificationRecipient[];
  template: NotificationTemplate;
  channels: NotificationChannel[];
  priority: NotificationPriority;
}
```

#### Notification Rules
- **Customer Notifications**: Notify customers of vendor actions
- **Vendor Notifications**: Notify vendors of customer submissions
- **Internal Notifications**: Notify internal teams of state changes
- **Escalation Notifications**: Notify supervisors of delays or issues
- **Reminder Notifications**: Remind users of pending actions

## Technical Specifications

### File Structure
```
src/
├── lib/
│   └── workflow/
│       ├── orderWorkflow.ts
│       ├── workflowEngine.ts
│       ├── stateValidators.ts
│       ├── transitionActions.ts
│       └── workflowTypes.ts
├── components/
│   └── workflow/
│       ├── OrderStateIndicator.tsx
│       ├── OrderActions.tsx
│       ├── StateProgress.tsx
│       ├── OrderHistory.tsx
│       └── TransitionConfirmation.tsx
├── hooks/
│   ├── useOrderWorkflow.ts
│   ├── useOrderActions.ts
│   └── useOrderHistory.ts
└── utils/
    ├── workflowHelpers.ts
    └── stateFormatters.ts
```

### Custom Hooks Implementation

#### useOrderWorkflow Hook
```typescript
function useOrderWorkflow(orderId: string) {
  const [order, setOrder] = useState<Order | null>(null);
  const [availableActions, setAvailableActions] = useState<TransitionTrigger[]>([]);
  const [loading, setLoading] = useState(false);
  
  const transitionOrder = useCallback(async (
    trigger: TransitionTrigger, 
    context?: TransitionContext
  ) => {
    setLoading(true);
    try {
      const result = await workflowEngine.transitionOrder(orderId, trigger, context);
      setOrder(result.order);
      return result;
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  }, [orderId]);
  
  const getAvailableActions = useCallback(() => {
    if (!order) return [];
    return workflowEngine.getAvailableTransitions(order.state, currentUser);
  }, [order, currentUser]);
  
  return {
    order,
    availableActions: getAvailableActions(),
    transitionOrder,
    loading,
    canTransition: (trigger: TransitionTrigger) => 
      availableActions.includes(trigger),
  };
}
```

### State Machine Configuration

#### Workflow Configuration
```typescript
const workflowConfig = {
  states: {
    [OrderState.DRAFT]: {
      label: 'Draft',
      description: 'Order is being prepared',
      color: 'gray',
      allowEditing: true,
      showProgress: false
    },
    [OrderState.SUBMITTED]: {
      label: 'Submitted',
      description: 'Waiting for vendor review',
      color: 'blue',
      allowEditing: false,
      showProgress: true
    },
    [OrderState.UNDER_REVIEW]: {
      label: 'Under Review',
      description: 'Vendor is reviewing your order',
      color: 'yellow',
      allowEditing: false,
      showProgress: true
    },
    [OrderState.APPROVED]: {
      label: 'Approved',
      description: 'Order approved by vendor',
      color: 'green',
      allowEditing: false,
      showProgress: false
    }
  },
  
  transitions: orderWorkflow,
  
  notifications: {
    [OrderState.SUBMITTED]: ['vendor_team'],
    [OrderState.APPROVED]: ['customer', 'customer_manager'],
    [OrderState.REJECTED]: ['customer', 'customer_manager']
  }
};
```

## Implementation Guidelines

### Development Approach

#### Phase 1: Core State Machine (Day 1-3)
1. Implement state machine engine and transition logic
2. Define all states and transition rules
3. Create state validation system
4. Set up audit trail tracking

#### Phase 2: UI Integration (Day 3-5)
1. Create state indicator components
2. Implement action buttons and confirmations
3. Build state progress visualization
4. Add order history display

#### Phase 3: Business Logic (Day 5-6)
1. Implement all transition actions
2. Add notification integration
3. Create validation rules
4. Add permission checking

#### Phase 4: Testing and Polish (Day 6-7)
1. Comprehensive workflow testing
2. Edge case handling
3. Performance optimization
4. Documentation and examples

### Testing Strategy

#### Unit Testing
- Test state machine transitions and validations
- Test workflow engine methods
- Test state validators and conditions
- Test transition actions and side effects

#### Integration Testing
- Test complete workflow scenarios
- Test notification integration
- Test audit trail functionality
- Test permission enforcement

#### Business Logic Testing
- Test all valid state transitions
- Test invalid transition prevention
- Test business rule enforcement
- Test error handling and recovery

## Success Criteria

### Functional Success Criteria
- [ ] All order states and transitions work correctly
- [ ] Business rules are properly enforced
- [ ] Audit trail captures all state changes
- [ ] Notifications are sent appropriately
- [ ] User interface reflects current state accurately

### Technical Success Criteria
- [ ] State machine is robust and reliable
- [ ] Performance is acceptable for complex workflows
- [ ] Error handling is comprehensive
- [ ] Code is maintainable and extensible
- [ ] Integration with other systems is seamless

### Business Success Criteria
- [ ] Workflow matches business requirements
- [ ] Compliance and audit requirements are met
- [ ] User experience is intuitive and clear
- [ ] Vendor integration points are well-defined
- [ ] Scalability supports business growth

## Future Considerations

### Advanced Workflow Features
- **Parallel Workflows**: Support for parallel approval processes
- **Conditional Workflows**: Dynamic workflows based on order characteristics
- **Escalation Rules**: Automatic escalation for delayed actions
- **Workflow Templates**: Configurable workflow templates
- **Integration APIs**: APIs for external workflow integration

### Business Process Enhancement
- **SLA Management**: Service level agreement tracking
- **Performance Metrics**: Workflow performance analytics
- **Process Optimization**: Continuous workflow improvement
- **Compliance Reporting**: Enhanced compliance and audit reporting
- **Multi-tenant Workflows**: Customer-specific workflow configurations

---

**Document Version**: 1.0  
**Last Updated**: January 2025  
**Prepared By**: Software Team Leader  
**Review Status**: Ready for Implementation
