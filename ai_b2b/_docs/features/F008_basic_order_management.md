# F008 - Basic Order Management

## Feature Overview

**Priority**: P0 - Critical (Foundation)  
**Phase**: 4 - Order Management Core  
**Estimated Effort**: 6-8 days  
**Dependencies**: F006 - Product Catalog Structure, F007 - Basic Search and Navigation

## Business Context

Basic Order Management implements the core B2B ordering functionality for the KAUST B2B Marketplace. This feature establishes the foundation for the unified Order = Quotation = Cart concept, enabling users to create, manage, and track their procurement requests through the B2B workflow.

## Technical Objectives

1. **Order Data Model**: Implement comprehensive order/quotation data structures
2. **Order Operations**: Create, read, update, and delete order functionality
3. **Multi-Order Support**: Enable users to manage multiple orders simultaneously
4. **Order Context**: Maintain active order context throughout the application
5. **B2B Workflow Foundation**: Prepare for advanced B2B workflow states

## Functional Requirements

### Order Data Structure

#### Core Order Model
```typescript
interface Order {
  // Basic Information
  id: string;
  orderNumber: string;
  name: string;
  description?: string;
  
  // Order Context
  customerId: string;
  userId: string;
  organizationId: string;
  departmentId?: string;
  
  // Order Items
  items: OrderItem[];
  itemCount: number;
  totalAmount: number;
  currency: string;
  
  // Status and Workflow
  status: OrderStatus;
  priority: OrderPriority;
  
  // Dates and Timing
  createdAt: Date;
  updatedAt: Date;
  submittedAt?: Date;
  requiredBy?: Date;
  
  // Additional Information
  notes?: string;
  attachments: OrderAttachment[];
  tags: string[];
  
  // Metadata
  version: number;
  isActive: boolean;
  lastModifiedBy: string;
}

interface OrderItem {
  id: string;
  productId: string;
  product: Product;
  variantId?: string;
  variant?: ProductVariant;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  targetPrice?: number;
  notes?: string;
  customizations?: Record<string, any>;
  addedAt: Date;
}
```

#### Order Status Types
```typescript
type OrderStatus = 
  | 'draft'           // User is building the order
  | 'saved'           // Order saved for later
  | 'submitted'       // Submitted for review
  | 'under_review'    // Being reviewed by vendor
  | 'approved'        // Approved by vendor
  | 'rejected'        // Rejected by vendor
  | 'expired'         // Expired without action
  | 'cancelled';      // Cancelled by user

type OrderPriority = 'low' | 'normal' | 'high' | 'urgent';
```

### Order Operations

#### Core Order Management
```typescript
interface OrderService {
  // Order CRUD Operations
  createOrder(orderData: CreateOrderRequest): Promise<Order>;
  getOrder(orderId: string): Promise<Order>;
  updateOrder(orderId: string, updates: UpdateOrderRequest): Promise<Order>;
  deleteOrder(orderId: string): Promise<void>;
  
  // Order Listing and Filtering
  getOrders(filters?: OrderFilters): Promise<OrderList>;
  getUserOrders(userId: string, filters?: OrderFilters): Promise<OrderList>;
  
  // Order Item Management
  addItemToOrder(orderId: string, item: AddOrderItemRequest): Promise<OrderItem>;
  updateOrderItem(orderId: string, itemId: string, updates: UpdateOrderItemRequest): Promise<OrderItem>;
  removeItemFromOrder(orderId: string, itemId: string): Promise<void>;
  
  // Order Actions
  submitOrder(orderId: string): Promise<Order>;
  cancelOrder(orderId: string, reason?: string): Promise<Order>;
  duplicateOrder(orderId: string, newName?: string): Promise<Order>;
}
```

#### Order State Management
- **Active Order Context**: Maintain currently active order for adding products
- **Order Switching**: Quick switching between different orders
- **Order Persistence**: Save order state across browser sessions
- **Optimistic Updates**: Immediate UI updates with background synchronization
- **Conflict Resolution**: Handle conflicts when multiple users edit same order

### Multi-Order Management

#### Order Organization
```typescript
interface OrderFilters {
  status?: OrderStatus[];
  priority?: OrderPriority[];
  dateRange?: DateRange;
  searchQuery?: string;
  tags?: string[];
  department?: string;
}

interface OrderList {
  orders: Order[];
  pagination: PaginationInfo;
  totalCount: number;
  filters: OrderFilters;
}
```

#### Order Context Switching
- **Order Selector**: Dropdown component for switching active orders
- **Quick Actions**: Create new order, duplicate existing order
- **Order Preview**: Quick preview of order contents without navigation
- **Order Badges**: Visual indicators for order status and item count
- **Recent Orders**: Quick access to recently accessed orders

### Order Item Management

#### Adding Products to Orders
- **Product Selection**: Add products from catalog to active order
- **Quantity Management**: Specify quantities with validation
- **Variant Selection**: Choose product variants when adding to order
- **Bulk Addition**: Add multiple products at once
- **Quick Add**: One-click add to order from product cards

#### Order Item Operations
- **Quantity Updates**: Modify item quantities with real-time total updates
- **Item Removal**: Remove items with confirmation
- **Item Notes**: Add notes or special instructions to items
- **Target Pricing**: Set target prices for negotiation
- **Item Customization**: Handle product customizations and configurations

### Order Display and UI

#### Order List View
- **Order Cards**: Compact order information display
- **Status Indicators**: Visual status indicators and progress
- **Quick Actions**: Edit, view, duplicate, delete actions
- **Sorting and Filtering**: Sort by date, status, total amount
- **Search**: Search orders by name, number, or content

#### Order Detail View
- **Order Header**: Order information and status
- **Items Table**: Detailed view of all order items
- **Order Summary**: Total calculations and summary information
- **Action Buttons**: Submit, save, cancel, and other actions
- **Order History**: Track changes and modifications

#### Order Creation Flow
- **Order Setup**: Create new order with name and basic information
- **Product Addition**: Add products through various methods
- **Review and Edit**: Review order contents before submission
- **Submission**: Submit order with confirmation
- **Confirmation**: Order submission confirmation and next steps

## Technical Specifications

### File Structure
```
src/
├── lib/
│   └── orders/
│       ├── orderService.ts
│       ├── orderTypes.ts
│       ├── orderUtils.ts
│       └── orderValidation.ts
├── components/
│   └── orders/
│       ├── OrderList.tsx
│       ├── OrderCard.tsx
│       ├── OrderDetail.tsx
│       ├── OrderSelector.tsx
│       ├── OrderItemTable.tsx
│       ├── AddToOrderButton.tsx
│       └── CreateOrderModal.tsx
├── hooks/
│   ├── useOrders.ts
│   ├── useActiveOrder.ts
│   ├── useOrderItems.ts
│   └── useOrderActions.ts
├── pages/
│   └── orders/
│       ├── page.tsx (Order list)
│       ├── [id]/
│       │   └── page.tsx (Order detail)
│       └── create/
│           └── page.tsx (Create order)
└── store/
    └── slices/
        └── ordersSlice.ts
```

### Custom Hooks Implementation

#### useActiveOrder Hook
```typescript
function useActiveOrder() {
  const dispatch = useAppDispatch();
  const activeOrderId = useAppSelector(selectActiveOrderId);
  const activeOrder = useAppSelector(selectActiveOrder);
  
  const setActiveOrder = useCallback((orderId: string) => {
    dispatch(setActiveOrderId(orderId));
  }, [dispatch]);
  
  const addItemToActiveOrder = useCallback(async (productId: string, quantity: number) => {
    if (!activeOrderId) {
      // Create new order if none active
      const newOrder = await dispatch(createOrderThunk({ name: 'My Order' }));
      dispatch(setActiveOrderId(newOrder.payload.id));
      return dispatch(addOrderItemThunk({ 
        orderId: newOrder.payload.id, 
        productId, 
        quantity 
      }));
    }
    
    return dispatch(addOrderItemThunk({ orderId: activeOrderId, productId, quantity }));
  }, [activeOrderId, dispatch]);
  
  return {
    activeOrder,
    activeOrderId,
    setActiveOrder,
    addItemToActiveOrder,
    hasActiveOrder: !!activeOrderId,
  };
}
```

#### useOrders Hook
```typescript
function useOrders(filters?: OrderFilters) {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const loadOrders = useCallback(async () => {
    try {
      setLoading(true);
      const result = await orderService.getOrders(filters);
      setOrders(result.orders);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [filters]);
  
  const createOrder = useCallback(async (orderData: CreateOrderRequest) => {
    const newOrder = await orderService.createOrder(orderData);
    setOrders(prev => [newOrder, ...prev]);
    return newOrder;
  }, []);
  
  return {
    orders,
    loading,
    error,
    createOrder,
    refetch: loadOrders,
  };
}
```

### Redux Integration

#### Orders Slice
```typescript
interface OrdersState {
  orders: Record<string, Order>;
  activeOrderId: string | null;
  orderList: string[];
  loading: boolean;
  error: string | null;
  filters: OrderFilters;
}

const ordersSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    setActiveOrderId: (state, action) => {
      state.activeOrderId = action.payload;
    },
    addOrder: (state, action) => {
      const order = action.payload;
      state.orders[order.id] = order;
      state.orderList.unshift(order.id);
    },
    updateOrder: (state, action) => {
      const { id, updates } = action.payload;
      if (state.orders[id]) {
        state.orders[id] = { ...state.orders[id], ...updates };
      }
    },
    removeOrder: (state, action) => {
      const orderId = action.payload;
      delete state.orders[orderId];
      state.orderList = state.orderList.filter(id => id !== orderId);
      if (state.activeOrderId === orderId) {
        state.activeOrderId = null;
      }
    },
  },
});
```

## Implementation Guidelines

### Development Approach

#### Phase 1: Core Data Model (Day 1-2)
1. Define order and order item data structures
2. Implement basic order service with CRUD operations
3. Set up Redux slice for order state management
4. Create mock data for development

#### Phase 2: Basic UI Components (Day 2-4)
1. Create order list and card components
2. Implement order detail view
3. Build order selector for active order switching
4. Add basic order creation flow

#### Phase 3: Order Item Management (Day 4-6)
1. Implement add to order functionality
2. Create order item management components
3. Add quantity updates and item removal
4. Integrate with product catalog

#### Phase 4: Advanced Features (Day 6-8)
1. Add order filtering and search
2. Implement order actions (submit, cancel, duplicate)
3. Add order validation and error handling
4. Optimize performance and user experience

### Testing Strategy

#### Unit Testing
- Test order service methods and validation
- Test Redux slice reducers and actions
- Test custom hooks behavior
- Test utility functions and helpers

#### Integration Testing
- Test order creation and management flows
- Test integration with product catalog
- Test state management and persistence
- Test API integration and error handling

#### User Experience Testing
- Test order management workflows
- Test multi-order switching and context
- Test mobile responsiveness
- Test accessibility compliance

## Success Criteria

### Functional Success Criteria
- [ ] Users can create and manage multiple orders
- [ ] Order item management works correctly
- [ ] Active order context is maintained properly
- [ ] Order status and workflow function as expected
- [ ] Order data persists across sessions

### Technical Success Criteria
- [ ] All order operations are type-safe and reliable
- [ ] Performance meets requirements for large orders
- [ ] State management is efficient and consistent
- [ ] Integration with other features is seamless
- [ ] Error handling is comprehensive and user-friendly

### User Experience Success Criteria
- [ ] Order management is intuitive and efficient
- [ ] Multi-order switching is smooth and clear
- [ ] Mobile experience is optimized
- [ ] Loading states and feedback are appropriate
- [ ] Error messages are clear and actionable

## Future Considerations

### B2B Workflow Enhancement
- **Approval Workflows**: Multi-level approval processes
- **Collaboration**: Team collaboration on orders
- **Templates**: Order templates for recurring purchases
- **Scheduling**: Scheduled and recurring orders
- **Integration**: ERP and procurement system integration

### Advanced Features
- **Order Analytics**: Order performance and analytics
- **Bulk Operations**: Bulk order management
- **Export/Import**: Order data export and import
- **Audit Trail**: Comprehensive order audit logging
- **Notifications**: Real-time order notifications

---

**Document Version**: 1.0  
**Last Updated**: January 2025  
**Prepared By**: Software Team Leader  
**Review Status**: Ready for Implementation
